const db = require('../utils/db');

class Device {
  // 根据设备令牌获取设备
  static async getByToken(deviceToken) {
    const sql = 'SELECT * FROM devices WHERE device_token = ? AND status = "active"';
    const [results] = await db.query(sql, [deviceToken]);
    return results.length ? results[0] : null;
  }

  // 根据ID获取设备
  static async getById(id) {
    const sql = 'SELECT * FROM devices WHERE id = ?';
    const [results] = await db.query(sql, [id]);
    return results.length ? results[0] : null;
  }

  // 获取用户的所有设备
  static async getAllByUserId(userId) {
    const sql = 'SELECT * FROM devices WHERE user_id = ? ORDER BY id DESC';
    const [results] = await db.query(sql, [userId]);
    return results;
  }

  // 获取用户设备总数
  static async countByUserId(userId) {
    const sql = 'SELECT COUNT(*) as count FROM devices WHERE user_id = ?';
    const [results] = await db.query(sql, [userId]);
    return results[0].count;
  }

  // 获取用户的设备（带分页）
  static async getAllByUserIdWithPagination(userId, limit = 20, offset = 0) {
    const sql = 'SELECT * FROM devices WHERE user_id = ? ORDER BY id DESC LIMIT ? OFFSET ?';
    const [results] = await db.query(sql, [userId, limit, offset]);
    return results;
  }

  // 创建设备
  static async create(deviceData) {
    const { user_id, device_name, device_token } = deviceData;
    const sql = 'INSERT INTO devices (user_id, device_name, device_token) VALUES (?, ?, ?)';
    const [result] = await db.query(sql, [user_id, device_name, device_token]);
    return result.insertId;
  }

  // 更新设备
  static async update(id, deviceData) {
    const { device_name, device_token, status } = deviceData;
    const sql = 'UPDATE devices SET device_name = ?, device_token = ?, status = ? WHERE id = ?';
    const [result] = await db.query(sql, [device_name, device_token, status, id]);
    return result;
  }

  // 删除设备
  static async delete(id) {
    const sql = 'DELETE FROM devices WHERE id = ?';
    const [result] = await db.query(sql, [id]);
    return result;
  }

  // 更新设备活跃时间
  static async updateActiveTime(id) {
    const sql = 'UPDATE devices SET last_active_time = NOW() WHERE id = ?';
    const [result] = await db.query(sql, [id]);
    return result;
  }

  // 获取设备的今日操作数
  static async getTodayOperationCount(deviceId) {
    const sql = `
      SELECT COUNT(*) as count 
      FROM operation_logs 
      WHERE device_id = ? 
      AND DATE(created_at) = CURDATE()
    `;
    const [results] = await db.query(sql, [deviceId]);
    return results[0].count;
  }

  // 获取设备的总操作数
  static async getTotalOperationCount(deviceId) {
    const sql = 'SELECT COUNT(*) as count FROM operation_logs WHERE device_id = ?';
    const [results] = await db.query(sql, [deviceId]);
    return results[0].count;
  }

  // 获取设备的成功率
  static async getSuccessRate(deviceId) {
    const sql = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success
      FROM operation_logs 
      WHERE device_id = ?
    `;
    const [results] = await db.query(sql, [deviceId]);
    if (results[0].total === 0) return 0;
    return (results[0].success / results[0].total) * 100;
  }

  // 获取在线设备
  static async getOnlineDevices(userId) {
    const onlineThreshold = 5 * 60; // 5分钟内有活动的设备视为在线
    const sql = `
      SELECT * FROM devices
      WHERE user_id = ? AND status = 'active'
      AND TIMESTAMPDIFF(SECOND, last_active_time, NOW()) < ?
    `;

    const [results] = await db.query(sql, [userId, onlineThreshold]);
    return results;
  }

  // 获取最近活跃的设备
  static async getRecentActiveDevices(userId, limit = 5) {
    try {
      const sql = `
        SELECT d.*,
          COUNT(ol.id) as operation_count,
          MAX(ol.created_at) as last_operation_time
        FROM devices d
        LEFT JOIN operation_logs ol ON d.id = ol.device_id
        WHERE d.user_id = ? AND d.status = 'active'
        GROUP BY d.id
        ORDER BY last_operation_time DESC, d.last_active_time DESC
        LIMIT ?
      `;

      const [results] = await db.query(sql, [userId, limit]);
      return results || [];
    } catch (error) {
      console.error('获取最近活跃设备错误:', error);
      return [];
    }
  }

  // 删除N天前创建的设备
  static async batchDeleteByDaysAgo(userId, days) {
    const sql = 'DELETE FROM devices WHERE user_id = ? AND created_at < DATE_SUB(CURDATE(), INTERVAL ? DAY)';
    const [result] = await db.query(sql, [userId, days]);
    return result.affectedRows || 0;
  }

  // 删除指定日期之前创建的设备
  static async batchDeleteBeforeDate(userId, beforeDate) {
    const sql = 'DELETE FROM devices WHERE user_id = ? AND DATE(created_at) < ?';
    const [result] = await db.query(sql, [userId, beforeDate]);
    return result.affectedRows || 0;
  }

  // 删除从未活跃的设备
  static async batchDeleteNeverActive(userId) {
    const sql = 'DELETE FROM devices WHERE user_id = ? AND last_active_time IS NULL';
    const [result] = await db.query(sql, [userId]);
    return result.affectedRows || 0;
  }

  // 删除所有设备
  static async batchDeleteAll(userId) {
    const sql = 'DELETE FROM devices WHERE user_id = ?';
    const [result] = await db.query(sql, [userId]);
    return result.affectedRows || 0;
  }
}

module.exports = Device; 
module.exports = {
  apps: [{
    name: 'xiaohongshu-server',
    script: 'app.js',
    instances: 1, // 单实例，避免数据库并发问题
    autorestart: true,
    watch: false,
    max_memory_restart: '2G', // 内存超过2G自动重启
    node_args: '--max-old-space-size=4096', // 增加堆内存限制
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    env_development: {
      NODE_ENV: 'development',
      PORT: 3001
    },
    // 错误日志
    error_file: './logs/err.log',
    // 输出日志
    out_file: './logs/out.log',
    // 日志文件
    log_file: './logs/combined.log',
    // 时间格式
    time: true,
    // 自动重启配置
    restart_delay: 4000, // 重启延迟4秒
    max_restarts: 10, // 最大重启次数
    min_uptime: '10s', // 最小运行时间
    // 监控配置
    monitoring: false,
    // 内存监控
    max_memory_restart: '2048M',
    // 实例配置
    exec_mode: 'fork', // 使用fork模式而不是cluster
    // 环境变量
    env_file: '.env'
  }]
};

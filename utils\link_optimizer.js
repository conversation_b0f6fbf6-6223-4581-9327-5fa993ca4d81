/**
 * 链接分发优化器
 * 独立的优化方案，不影响原有逻辑
 * 通过设置开关控制是否启用
 */

const db = require('./db');
const Setting = require('../models/setting');
const linkCache = require('./link_cache');
const linkQueue = require('./link_queue');

class LinkOptimizer {
  
  /**
   * 优化版的获取下一个可用链接
   * 集成了缓存、队列、批量查询等优化
   */
  static async getNextAvailableLink(userId, deviceId) {
    try {
      console.log(`🚀 优化器启动 - 用户(${userId}), 设备(${deviceId})`);
      
      // 获取优化设置
      const useCache = await Setting.getValue(userId, 'optimization.use_cache', 'true') === 'true';
      const useQueue = await Setting.getValue(userId, 'optimization.use_queue', 'false') === 'true';
      const allocationMode = await Setting.getValue(userId, 'link.allocation_mode', 'smart');
      
      // 如果启用队列处理
      if (useQueue) {
        console.log(`📋 使用队列处理模式`);
        return await linkQueue.addRequest(userId, deviceId, { useCache, allocationMode });
      }
      
      // 直接处理（使用缓存优化）
      if (allocationMode === 'round_robin') {
        return await this.getNextRoundRobinLinkOptimized(userId, deviceId, useCache);
      } else {
        return await this.getNextSmartLinkOptimized(userId, deviceId, useCache);
      }
      
    } catch (error) {
      console.error('链接优化器错误:', error);
      // 降级到原有逻辑
      console.log('🔄 降级到原有逻辑');
      const Link = require('../models/link');
      return await Link.getNextAvailableLink(userId, deviceId);
    }
  }

  /**
   * 优化版智能分配
   */
  static async getNextSmartLinkOptimized(userId, deviceId, useCache = true) {
    try {
      const LinkDeviceAssignment = require('../models/link_device_assignment');
      
      // 获取设置
      const maxRetries = await Setting.getValue(userId, 'link.max_retries', '3');
      const maxConcurrentOps = await Setting.getValue(userId, 'link.max_concurrent_operations', '3');
      const cooldownSeconds = await Setting.getValue(userId, 'link.cooldown_seconds', '300');

      console.log(`🎯 智能分配优化版 - 缓存:${useCache}`);

      let availableLinks;
      
      if (useCache) {
        // 使用缓存获取可用链接
        availableLinks = await linkCache.getAvailableLinks(userId, deviceId, maxRetries, maxConcurrentOps, cooldownSeconds);
      } else {
        // 直接查询（批量优化）
        availableLinks = await this.fetchAvailableLinksOptimized(userId, deviceId, maxRetries, maxConcurrentOps, cooldownSeconds);
      }

      if (availableLinks.length === 0) {
        console.log('📭 没有可用链接');
        return null;
      }

      // 选择第一个可用链接
      const selectedLink = availableLinks[0];
      
      try {
        await LinkDeviceAssignment.create(selectedLink.id, deviceId);
        const priority = selectedLink.task_priority || selectedLink.link_priority || 'unknown';
        console.log(`✅ 设备(${deviceId}) → 链接(${selectedLink.id})[${priority}] (优化版)`);
        
        // 清除缓存（因为链接状态发生变化）
        if (useCache) {
          linkCache.invalidateUserCache(userId);
        }
        
        return selectedLink;
      } catch (error) {
        console.error(`创建分配记录失败:`, error);
        return null;
      }
      
    } catch (error) {
      console.error('智能分配优化版错误:', error);
      throw error;
    }
  }

  /**
   * 优化版循环分配
   */
  static async getNextRoundRobinLinkOptimized(userId, deviceId, useCache = true) {
    try {
      const LinkDeviceAssignment = require('../models/link_device_assignment');
      const RoundRobinState = require('../models/round_robin_state');
      
      // 获取设置
      const maxRetries = await Setting.getValue(userId, 'link.max_retries', '3');
      const maxConcurrentOps = await Setting.getValue(userId, 'link.max_concurrent_operations', '3');
      const cooldownSeconds = await Setting.getValue(userId, 'link.cooldown_seconds', '300');

      console.log(`🔄 循环分配优化版 - 缓存:${useCache}`);

      // 按优先级顺序检查
      const priorities = ['high', 'medium', 'low'];

      for (const priority of priorities) {
        console.log(`🔍 检查${priority}优先级链接...`);

        let priorityLinks;
        
        if (useCache) {
          // 从缓存获取该优先级的链接
          const allLinks = await linkCache.getAvailableLinks(userId, deviceId, maxRetries, maxConcurrentOps, cooldownSeconds);
          priorityLinks = allLinks.filter(link => {
            const linkPriority = link.task_priority || link.link_priority;
            return linkPriority === priority;
          });
        } else {
          // 直接查询该优先级的链接
          priorityLinks = await this.fetchPriorityLinksOptimized(userId, deviceId, priority, maxRetries, maxConcurrentOps, cooldownSeconds);
        }

        if (priorityLinks.length === 0) {
          console.log(`📭 ${priority}优先级没有可用链接`);
          continue;
        }

        console.log(`📋 ${priority}优先级有 ${priorityLinks.length} 个可用链接`);

        // 在该优先级内部循环
        for (let attempt = 0; attempt < priorityLinks.length; attempt++) {
          const roundRobinIndex = await RoundRobinState.getNextRoundRobinIndex(userId, priority, priorityLinks.length);
          const selectedLink = priorityLinks[roundRobinIndex];

          try {
            await LinkDeviceAssignment.create(selectedLink.id, deviceId);
            console.log(`✅ 设备(${deviceId}) → 链接(${selectedLink.id})[${priority}] 索引:${roundRobinIndex} (优化版)`);
            
            // 清除缓存
            if (useCache) {
              linkCache.invalidateUserCache(userId);
            }
            
            return selectedLink;
          } catch (error) {
            console.error(`创建分配记录失败:`, error);
            continue;
          }
        }

        console.log(`🚫 ${priority}优先级所有链接分配失败`);
      }

      console.log('🔄 所有优先级都检查完毕，无可用链接');
      return null;
      
    } catch (error) {
      console.error('循环分配优化版错误:', error);
      throw error;
    }
  }

  /**
   * 批量查询可用链接（优化版）
   */
  static async fetchAvailableLinksOptimized(userId, deviceId, maxRetries, maxConcurrentOps, cooldownSeconds) {
    const batchDataSql = `
      SELECT 
        l.id,
        l.url,
        l.task_id,
        l.priority as link_priority,
        l.created_at,
        l.fail_count,
        t.priority as task_priority,
        
        -- 设备操作历史
        (SELECT COUNT(*) FROM operation_logs ol 
         WHERE ol.device_id = ? AND ol.link_id = l.id) as device_operated_count,
        
        -- 最后操作时间
        (SELECT MAX(ol2.created_at) FROM operation_logs ol2 
         WHERE ol2.link_id = l.id AND ol2.user_id = ?) as last_operation_time,
        
        -- 当前并发数
        (SELECT COUNT(*) FROM link_device_assignments lda 
         WHERE lda.link_id = l.id AND lda.status = 'active') as current_concurrent_count
         
      FROM links l
      LEFT JOIN tasks t ON l.task_id = t.id
      WHERE l.user_id = ?
      AND l.status = 'active'
      AND l.fail_count < ?
      ORDER BY
        CASE t.priority
          WHEN 'high' THEN 1
          WHEN 'medium' THEN 2
          WHEN 'low' THEN 3
          ELSE 4
        END,
        CASE l.priority
          WHEN 'high' THEN 1
          WHEN 'medium' THEN 2
          WHEN 'low' THEN 3
          ELSE 4
        END,
        l.created_at ASC
      LIMIT 100
    `;

    const [candidateLinks] = await db.query(batchDataSql, [deviceId, userId, userId, maxRetries]);
    
    // 在内存中快速筛选
    const now = new Date();
    const availableLinks = [];
    
    for (const link of candidateLinks) {
      // 检查设备是否已操作过
      if (link.device_operated_count > 0) continue;
      
      // 检查冷却时间
      if (link.last_operation_time) {
        const lastOpTime = new Date(link.last_operation_time);
        const diffSeconds = Math.floor((now - lastOpTime) / 1000);
        if (diffSeconds < cooldownSeconds) continue;
      }
      
      // 检查并发限制
      if (link.current_concurrent_count >= maxConcurrentOps) continue;
      
      availableLinks.push(link);
      
      // 限制返回数量，避免过度处理
      if (availableLinks.length >= 20) break;
    }
    
    return availableLinks;
  }

  /**
   * 获取特定优先级的可用链接
   */
  static async fetchPriorityLinksOptimized(userId, deviceId, priority, maxRetries, maxConcurrentOps, cooldownSeconds) {
    const allLinks = await this.fetchAvailableLinksOptimized(userId, deviceId, maxRetries, maxConcurrentOps, cooldownSeconds);
    
    return allLinks.filter(link => {
      const linkPriority = link.task_priority || link.link_priority;
      return linkPriority === priority;
    });
  }

  /**
   * 获取优化器统计信息
   */
  static getStats() {
    return {
      cache: linkCache.getStats(),
      queue: linkQueue.getStats(),
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = LinkOptimizer;

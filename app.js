const express = require('express');
const path = require('path');
const session = require('express-session');
const bodyParser = require('body-parser');
const expressLayouts = require('express-ejs-layouts');
const config = require('./config/config');
const db = require('./utils/db');
const apiRoutes = require('./routes/api');
const webRoutes = require('./routes/web');
const Setting = require('./models/setting');
const User = require('./models/user');
const memoryMonitor = require('./utils/memoryMonitor');
// 引入http模块用于创建WebSocket服务器
const http = require('http');
// 添加CORS中间件
const cors = require('cors');

// 创建Express应用
const app = express();
// 创建HTTP服务器
const server = http.createServer(app);
// 创建Socket.IO服务
const io = require('socket.io')(server);

// 配置CORS，允许跨域请求
app.use(cors({
  origin: '*',                      // 允许所有来源
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], // 允许的HTTP方法
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept'], // 允许的请求头
  credentials: true,                // 允许携带凭证
  preflightContinue: false,         // 不将预检请求传递给下一个处理程序
  optionsSuccessStatus: 204         // 预检请求的成功状态码
}));

// 处理OPTIONS预检请求
app.options('*', cors());

// WebSocket事件处理
io.on('connection', (socket) => {
  console.log('新的WebSocket连接建立');
  
  // 认证
  socket.on('authenticate', async (data) => {
    try {
      // 验证用户会话
      if (data.sessionId) {
        // 这里需要从会话存储中获取用户ID
        // 为简化，我们假设直接传递了userId
        const userId = data.userId;
        if (userId) {
          // 将socket加入用户特定的房间
          socket.join(`user_${userId}`);
          socket.userId = userId;
          socket.emit('authenticated', { success: true });
          console.log(`用户 ${userId} WebSocket认证成功`);
        } else {
          socket.emit('authenticated', { success: false, message: '无效的会话' });
        }
      } else {
        socket.emit('authenticated', { success: false, message: '未提供会话ID' });
      }
    } catch (error) {
      console.error('WebSocket认证错误:', error);
      socket.emit('authenticated', { success: false, message: '认证错误' });
    }
  });
  
  // 断开连接
  socket.on('disconnect', () => {
    console.log('WebSocket连接断开');
  });
});

// 导出Socket.IO实例，供其他模块使用
app.io = io;

// 配置视图引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use(expressLayouts);
app.set('layout', 'layout');
app.set('layout extractScripts', true);
app.set('layout extractStyles', true);

// 配置中间件
app.use(express.static(path.join(__dirname, 'public')));
// 增加请求体大小限制，支持大量数据导入
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));

// 添加请求日志中间件
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  // 为所有响应添加CORS头
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type,Authorization');
  next();
});

// 配置会话
app.use(session({
  secret: config.server.session_secret,
  resave: false,
  saveUninitialized: false,
  cookie: { maxAge: 24 * 60 * 60 * 1000 } // 1天
}));

// 配置全局模板变量
app.use((req, res, next) => {
  res.locals.currentPath = req.path;
  next();
});

// 添加调试中间件
app.use((req, res, next) => {
  console.log(`[DEBUG] 请求路径: ${req.method} ${req.path}`);
  next();
});

// 添加全局设置中间件
app.use(async (req, res, next) => {
  if (req.session && req.session.userId) {
    try {
      // 获取用户设置
      const settings = await Setting.getAll(req.session.userId);
      
      // 将设置转换为对象格式
      const settingsMap = {};
      if (settings && Array.isArray(settings)) {
        settings.forEach(setting => {
          settingsMap[setting.setting_key] = setting.setting_value;
        });
      }
      
      // 获取默认设置
      const defaultSettings = Setting.getDefaultSettings();
      
      // 合并用户设置和默认设置
      const globalSettings = { ...defaultSettings, ...settingsMap };
      
      // 将设置添加到res.locals，使其在所有模板中可用
      res.locals.globalSettings = globalSettings;
      
      // 如果是GET请求且没有指定perPage参数，则使用设置中的每页显示条目数
      if (req.method === 'GET' && !req.query.perPage && globalSettings['general.items_per_page']) {
        // 为请求添加perPage参数
        req.query.perPage = globalSettings['general.items_per_page'];
      }
    } catch (error) {
      console.error('加载全局设置错误:', error);
    }
  }
  next();
});

// 添加API请求限制中间件，限制并发请求数量和请求速率
const apiRateLimiter = async (req, res, next) => {
  try {
    // 仅对API路由应用限制
    if (!req.path.startsWith('/api')) {
      return next();
    }

    // 获取用户名
    const username = req.body?.username || req.query?.username;
    if (!username) {
      return res.status(400).json({
        success: false,
        message: '缺少用户名'
      });
    }

    // 获取用户
    const user = await User.getByUsername(username);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '无效的用户名'
      });
    }

    // 从用户设置中获取限制参数
    const maxConcurrentRequests = parseInt(await Setting.getValue(user.id, 'system.max_concurrent_requests', '20'));
    const requestRateLimit = parseInt(await Setting.getValue(user.id, 'system.request_rate_limit', '60'));
    const queueTimeoutSeconds = parseInt(await Setting.getValue(user.id, 'system.queue_timeout_seconds', '30'));

    // 请求记录存储（使用内存存储，也可以替换为Redis等）
    if (!global.apiRequestStats) {
      global.apiRequestStats = {};
    }

    // 初始化用户的请求统计
    if (!global.apiRequestStats[user.id]) {
      global.apiRequestStats[user.id] = {
        activeRequests: 0,
        requestHistory: [],
        queue: [],
        queueTimers: {}
      };
    }

    const stats = global.apiRequestStats[user.id];

    // 清理过期的请求历史
    const now = Date.now();
    stats.requestHistory = stats.requestHistory.filter(time => now - time < 60000); // 保留最近1分钟的记录

    // 检查请求速率
    if (stats.requestHistory.length >= requestRateLimit) {
      return res.status(429).json({
        success: false,
        message: '请求过于频繁，请稍后再试',
        retry_after: Math.ceil((60000 - (now - stats.requestHistory[0])) / 1000)
      });
    }

    // 检查并发请求数
    if (stats.activeRequests >= maxConcurrentRequests) {
      // 将请求加入队列
      const queueId = `${user.id}_${now}_${Math.random().toString(36).substring(2, 10)}`;
      
      // 设置队列超时
      const timeoutPromise = new Promise((resolve, reject) => {
        stats.queueTimers[queueId] = setTimeout(() => {
          // 从队列中移除
          stats.queue = stats.queue.filter(id => id !== queueId);
          delete stats.queueTimers[queueId];
          reject(new Error('请求队列等待超时'));
        }, queueTimeoutSeconds * 1000);
      });
      
      // 等待轮到这个请求
      const queuePromise = new Promise((resolve) => {
        stats.queue.push(queueId);
        
        // 检查队列的函数
        const checkQueue = () => {
          if (stats.queue[0] === queueId && stats.activeRequests < maxConcurrentRequests) {
            // 从队列中移除
            stats.queue.shift();
            clearTimeout(stats.queueTimers[queueId]);
            delete stats.queueTimers[queueId];
            resolve();
          } else {
            // 继续等待
            setTimeout(checkQueue, 100);
          }
        };
        
        // 开始检查队列
        checkQueue();
      });
      
      try {
        // 等待队列或超时
        await Promise.race([queuePromise, timeoutPromise]);
      } catch (error) {
        return res.status(429).json({
          success: false,
          message: '服务器繁忙，请求队列已满，请稍后再试',
          queue_timeout: queueTimeoutSeconds
        });
      }
    }

    // 记录请求
    stats.activeRequests++;
    stats.requestHistory.push(now);

    // 请求完成时更新计数
    res.on('finish', () => {
      stats.activeRequests--;
    });

    next();
  } catch (error) {
    console.error('API限流中间件错误:', error);
    next(error);
  }
};

// 配置路由
app.use('/', webRoutes);

// 为ping端点创建单独的路由器
const pingRouter = express.Router();
pingRouter.get('/ping', (req, res) => {
  res.json({
    success: true,
    message: 'API服务正常',
    timestamp: new Date().toISOString(),
    server_version: '1.0.0',
    cors_enabled: true
  });
});
app.use('/api', pingRouter);

// 在API路由前应用限流中间件
app.use(apiRateLimiter);
// 挂载API路由到应用根路径，直接使用/device-configs等路径
app.use('/', apiRoutes);

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).render('error', { message: '服务器错误' });
});

// 404处理
app.use((req, res, next) => {
  res.status(404).render('error', { message: '页面不存在' });
});

// 启动服务器
const port = config.server.port || 3000;
server.listen(port, async () => {
  try {
    // 测试数据库连接
    const connected = await db.testConnection();
    if (!connected) {
      console.error('数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    console.log(`✅ 服务器已在端口 ${port} 启动`);
    console.log(`🌐 Web界面: http://localhost:${port}`);
    console.log(`🔌 API接口: http://localhost:${port}/api`);
    console.log(`🔧 已应用并发修复优化`);

    // 启动内存监控
    memoryMonitor.start();
    console.log(`🔍 内存监控已启动`);

  } catch (error) {
    console.error('💥 服务器启动失败:', error);
    process.exit(1);
  }
});

module.exports = { app, io, server }; 
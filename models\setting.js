const db = require('../utils/db');
const { settingsCache } = require('../utils/simpleCache');

class Setting {
  // 获取所有设置
  static async getAll(userId) {
    try {
      const [rows] = await db.query(
        'SELECT * FROM settings WHERE user_id = ?',
        [userId]
      );
      return rows;
    } catch (error) {
      console.error('获取设置错误:', error);
      throw error;
    }
  }

  // 获取单个设置
  static async get(userId, key) {
    try {
      const [rows] = await db.query(
        'SELECT * FROM settings WHERE user_id = ? AND setting_key = ?',
        [userId, key]
      );
      return rows && rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error(`获取单个设置错误 (${key}):`, error);
      throw error;
    }
  }

  // 获取设置值（带缓存）
  static async getValue(userId, key, defaultValue = null) {
    try {
      const cacheKey = `${userId}:${key}`;

      // 先检查缓存
      const cachedValue = settingsCache.get(cacheKey);
      if (cachedValue !== null) {
        return cachedValue;
      }

      // 缓存未命中，从数据库获取
      const setting = await this.get(userId, key);
      const value = setting ? setting.setting_value : defaultValue;

      // 存入缓存
      settingsCache.set(cacheKey, value);

      return value;
    } catch (error) {
      console.error(`获取设置值错误 (${key}):`, error);
      return defaultValue;
    }
  }

  // 设置值
  static async set(userId, key, value, description = '') {
    try {
      console.log(`正在设置设置: ${key}=${value} 用户ID: ${userId}`);
      
      // 使用直接的SQL插入/更新语句，而不是REPLACE INTO
      // 首先检查记录是否存在
      const [existingRows] = await db.query(
        'SELECT * FROM settings WHERE user_id = ? AND setting_key = ?',
        [userId, key]
      );
      
      let result;
      if (existingRows && existingRows.length > 0) {
        // 更新现有记录
        [result] = await db.query(
          'UPDATE settings SET setting_value = ?, description = ?, updated_at = NOW() WHERE user_id = ? AND setting_key = ?',
          [value, description, userId, key]
        );
        console.log(`更新设置结果:`, result);
      } else {
        // 插入新记录
        [result] = await db.query(
          'INSERT INTO settings (user_id, setting_key, setting_value, description, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())',
          [userId, key, value, description]
        );
        console.log(`插入设置结果:`, result);
      }
      
      // 验证设置是否成功保存
      const verifyResult = await this.get(userId, key);
      if (!verifyResult) {
        console.error(`设置验证失败: ${key}=${value}, 未找到记录`);
        throw new Error(`设置保存失败: ${key} - 未找到记录`);
      }
      
      if (verifyResult.setting_value !== value) {
        console.error(`设置验证失败: ${key}=${value}, 实际值: ${verifyResult.setting_value}`);
        throw new Error(`设置保存验证失败: ${key} - 值不匹配`);
      }
      
      console.log(`成功设置: ${key}=${value}`);
      return true;
    } catch (error) {
      console.error(`设置值错误 (${key}=${value}):`, error);
      throw error;
    }
  }

  // 批量设置值
  static async setMultiple(userId, settings) {
    try {
      for (const [key, value] of Object.entries(settings)) {
        await this.set(userId, key, value);
      }
      return true;
    } catch (error) {
      console.error('批量设置值错误:', error);
      throw error;
    }
  }

  // 删除设置
  static async delete(userId, key) {
    try {
      const [result] = await db.query(
        'DELETE FROM settings WHERE user_id = ? AND setting_key = ?',
        [userId, key]
      );
      console.log(`删除设置结果:`, result);
      return true;
    } catch (error) {
      console.error(`删除设置错误 (${key}):`, error);
      throw error;
    }
  }

  // 获取默认设置
  static getDefaultSettings() {
    return {
      // 常规设置
      'general.app_name': '小红书点赞助手',
      'general.items_per_page': '20',
      
      // 任务设置
      'task.default_priority': 'medium',
      'task.default_target_likes': '100',
      'task.default_target_collects': '50',
      'task.default_target_comments': '0',
      
      // 设备设置
      'device.auto_assign': 'true',
      'device.max_operations_per_day': '100',
      'device.trust_threshold': '70',
      
      // 链接操作设置
      'link.max_concurrent_operations': '3',
      'link.cooldown_seconds': '300',  // 冷却时间（秒）
      'link.processing_timeout_seconds': '120',  // 处理超时时间（秒）
      'link.max_retries': '3',  // 最大重试次数
      'link.allocation_mode': 'smart',  // 链接分配模式：smart(智能分配) 或 round_robin(循环分配)
      'link.round_robin_reset_hours': '24',  // 循环计数器重置间隔（小时）
      'link.priority_strict_mode': 'true',  // 严格优先级模式
      'link.use_optimization': 'false',  // 启用链接分发性能优化（默认关闭）

      // 性能优化设置
      'optimization.use_cache': 'true',  // 启用链接缓存
      'optimization.use_queue': 'false',  // 启用请求队列处理
      'optimization.cache_ttl': '30',  // 缓存生存时间（秒）
      'optimization.max_concurrent_users': '10',  // 最大并发处理用户数
      'optimization.batch_size': '5',  // 批处理大小
      
      // 系统性能设置
      'system.max_concurrent_requests': '20', // 最大并发请求数
      'system.request_rate_limit': '60',  // 每分钟最大请求次数
      'system.queue_timeout_seconds': '30', // 请求队列超时时间（秒）
      
      // 通知设置
      'notification.email_enabled': 'false',
      'notification.email': '',
      'notification.task_completion': 'false',
      'notification.device_offline': 'false',
      'notification.error_threshold': '10'
    };
  }

  // 获取设置类别
  static getSettingCategories() {
    return {
      'general': '常规设置',
      'task': '任务设置',
      'device': '设备设置',
      'link': '链接操作设置',
      'optimization': '性能优化设置',
      'system': '系统性能设置',
      'notification': '通知设置'
    };
  }

  // 获取设置描述
  static getSettingDescriptions() {
    return {
      'general.app_name': '应用名称',
      'general.items_per_page': '每页显示条目数',
      
      'task.default_priority': '默认任务优先级',
      'task.default_target_likes': '默认目标点赞数',
      'task.default_target_collects': '默认目标收藏数',
      'task.default_target_comments': '默认目标评论数',
      
      'device.auto_assign': '自动分配任务给设备',
      'device.max_operations_per_day': '每个设备每日最大操作次数',
      'device.trust_threshold': '设备信任度阈值',
      
      'link.max_concurrent_operations': '同一链接最大并发操作数',
      'link.cooldown_seconds': '链接操作后冷却时间（秒）',
      'link.processing_timeout_seconds': '链接处理超时时间（秒）',
      'link.max_retries': '链接操作失败最大重试次数',
      'link.allocation_mode': '链接分配模式',
      'link.round_robin_reset_hours': '循环计数器重置间隔（小时）',
      'link.priority_strict_mode': '严格优先级模式',
      'link.use_optimization': '启用链接分发性能优化',

      'optimization.use_cache': '启用链接缓存',
      'optimization.use_queue': '启用请求队列处理',
      'optimization.cache_ttl': '缓存生存时间（秒）',
      'optimization.max_concurrent_users': '最大并发处理用户数',
      'optimization.batch_size': '批处理大小',
      
      'system.max_concurrent_requests': '最大并发请求数',
      'system.request_rate_limit': '每分钟最大请求次数',
      'system.queue_timeout_seconds': '请求队列超时时间（秒）',
      
      'notification.email_enabled': '启用邮件通知',
      'notification.email': '通知邮箱',
      'notification.task_completion': '任务完成通知',
      'notification.device_offline': '设备离线通知',
      'notification.error_threshold': '错误阈值通知'
    };
  }

  // 初始化用户设置
  static async initUserSettings(userId) {
    try {
      const defaultSettings = this.getDefaultSettings();
      await this.setMultiple(userId, defaultSettings);
      return true;
    } catch (error) {
      console.error('初始化用户设置错误:', error);
      throw error;
    }
  }
}

module.exports = Setting; 
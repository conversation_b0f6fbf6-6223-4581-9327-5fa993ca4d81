const rateLimit = require('express-rate-limit');

// 创建不同的限流器
const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs: windowMs,
    max: max,
    message: {
      error: message,
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    // 自定义键生成器，基于IP和用户ID
    keyGenerator: (req) => {
      return req.ip + ':' + (req.user?.id || 'anonymous');
    },
    // 跳过成功的请求计数（可选）
    skipSuccessfulRequests: false,
    // 跳过失败的请求计数（可选）
    skipFailedRequests: false,
  });
};

// API限流器 - 每分钟最多100个请求
const apiLimiter = createRateLimiter(
  60 * 1000, // 1分钟
  100, // 最多100个请求
  '请求过于频繁，请稍后再试'
);

// 链接获取限流器 - 每秒最多10个请求
const linkLimiter = createRateLimiter(
  1000, // 1秒
  10, // 最多10个请求
  '链接请求过于频繁，请稍后再试'
);

// 严格限流器 - 用于敏感操作
const strictLimiter = createRateLimiter(
  15 * 60 * 1000, // 15分钟
  5, // 最多5个请求
  '操作过于频繁，请15分钟后再试'
);

// 设备注册限流器
const deviceRegisterLimiter = createRateLimiter(
  60 * 1000, // 1分钟
  5, // 最多5个设备注册
  '设备注册过于频繁，请稍后再试'
);

module.exports = {
  apiLimiter,
  linkLimiter,
  strictLimiter,
  deviceRegisterLimiter
};

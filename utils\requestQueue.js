class RequestQueue {
  constructor() {
    this.queues = new Map(); // 每个用户一个队列
    this.processing = new Map(); // 正在处理的请求
    this.maxQueueSize = 100; // 每个用户最大队列长度
    this.defaultTimeout = 30000; // 默认30秒超时
  }

  // 添加请求到队列
  async enqueue(userId, deviceId, requestData, timeoutSeconds = 30) {
    const queueKey = `${userId}`;
    
    if (!this.queues.has(queueKey)) {
      this.queues.set(queueKey, []);
    }
    
    const queue = this.queues.get(queueKey);
    
    // 检查队列是否已满
    if (queue.length >= this.maxQueueSize) {
      throw new Error(`用户队列已满 (${queue.length}/${this.maxQueueSize})`);
    }
    
    const requestId = `${userId}_${deviceId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timeout = timeoutSeconds * 1000;
    
    const queueItem = {
      id: requestId,
      userId: userId,
      deviceId: deviceId,
      data: requestData,
      timestamp: Date.now(),
      timeout: timeout,
      promise: null,
      resolve: null,
      reject: null
    };
    
    // 创建Promise
    queueItem.promise = new Promise((resolve, reject) => {
      queueItem.resolve = resolve;
      queueItem.reject = reject;
      
      // 设置超时
      setTimeout(() => {
        if (queue.includes(queueItem)) {
          // 从队列中移除
          const index = queue.indexOf(queueItem);
          if (index > -1) {
            queue.splice(index, 1);
          }
          reject(new Error(`请求超时 (${timeoutSeconds}秒)`));
        }
      }, timeout);
    });
    
    queue.push(queueItem);
    console.log(`📋 请求加入队列: 用户(${userId}), 设备(${deviceId}), 队列长度: ${queue.length}`);
    
    // 尝试处理队列
    this.processQueue(queueKey);
    
    return queueItem.promise;
  }

  // 处理队列
  async processQueue(queueKey) {
    const queue = this.queues.get(queueKey);
    if (!queue || queue.length === 0) {
      return;
    }
    
    // 检查是否已经在处理
    if (this.processing.has(queueKey)) {
      return;
    }
    
    this.processing.set(queueKey, true);
    
    try {
      while (queue.length > 0) {
        const item = queue.shift();
        
        try {
          console.log(`⚡ 处理队列请求: ${item.id}`);
          
          // 这里应该调用实际的链接分发逻辑
          // 暂时返回一个模拟结果
          const result = await this.processRequest(item);
          
          item.resolve(result);
          console.log(`✅ 队列请求完成: ${item.id}`);
          
        } catch (error) {
          console.error(`❌ 队列请求失败: ${item.id}`, error.message);
          item.reject(error);
        }
        
        // 添加小延迟避免过快处理
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } finally {
      this.processing.delete(queueKey);
    }
  }

  // 处理单个请求（这里需要集成实际的业务逻辑）
  async processRequest(item) {
    // 这里应该调用Link.getNextAvailableLink或其他业务逻辑
    // 暂时返回模拟数据
    return {
      success: true,
      message: '队列处理完成',
      data: {
        linkId: Math.floor(Math.random() * 10000),
        deviceId: item.deviceId,
        processedAt: new Date().toISOString()
      }
    };
  }

  // 获取队列状态
  getQueueStatus(userId = null) {
    if (userId) {
      const queueKey = `${userId}`;
      const queue = this.queues.get(queueKey) || [];
      return {
        userId: userId,
        queueLength: queue.length,
        processing: this.processing.has(queueKey),
        items: queue.map(item => ({
          id: item.id,
          deviceId: item.deviceId,
          timestamp: item.timestamp,
          waitTime: Date.now() - item.timestamp
        }))
      };
    }
    
    // 返回所有用户的队列状态
    const status = {};
    for (const [queueKey, queue] of this.queues.entries()) {
      const userId = queueKey;
      status[userId] = {
        queueLength: queue.length,
        processing: this.processing.has(queueKey),
        oldestWaitTime: queue.length > 0 ? Date.now() - queue[0].timestamp : 0
      };
    }
    return status;
  }

  // 清理空队列
  cleanup() {
    for (const [queueKey, queue] of this.queues.entries()) {
      if (queue.length === 0 && !this.processing.has(queueKey)) {
        this.queues.delete(queueKey);
      }
    }
  }

  // 取消用户的所有请求
  cancelUserRequests(userId) {
    const queueKey = `${userId}`;
    const queue = this.queues.get(queueKey);
    
    if (queue) {
      queue.forEach(item => {
        item.reject(new Error('请求被取消'));
      });
      queue.length = 0;
    }
    
    this.processing.delete(queueKey);
    console.log(`🚫 取消用户(${userId})的所有队列请求`);
  }
}

// 创建全局实例
const requestQueue = new RequestQueue();

// 定期清理
setInterval(() => {
  requestQueue.cleanup();
}, 60000);

module.exports = requestQueue;

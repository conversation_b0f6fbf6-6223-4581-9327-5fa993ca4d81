const os = require('os');

class MemoryMonitor {
  constructor() {
    this.isMonitoring = false;
    this.checkInterval = 30000; // 30秒检查一次
    this.warningCount = 0;
    this.maxWarnings = 3; // 连续3次警告后采取行动
    this.lastGcTime = 0;
    this.gcCooldown = 10000; // GC冷却时间10秒

    // 动态计算内存阈值
    this.calculateMemoryThresholds();
  }

  // 动态计算内存阈值（基于Node.js启动时的内存限制）
  calculateMemoryThresholds() {
    // 获取Node.js的最大堆内存限制（通过V8引擎）
    const v8 = require('v8');
    const heapStats = v8.getHeapStatistics();
    const maxHeapSize = Math.round(heapStats.heap_size_limit / 1024 / 1024); // 转换为MB

    // 设置阈值为最大堆内存的百分比
    this.heapWarningThreshold = Math.round(maxHeapSize * 0.80); // 80%时警告
    this.heapCriticalThreshold = Math.round(maxHeapSize * 0.90); // 90%时危险

    // RSS阈值设置为堆内存限制的1.5倍（RSS通常比堆内存大）
    this.rssWarningThreshold = Math.round(maxHeapSize * 1.2); // 120%
    this.rssCriticalThreshold = Math.round(maxHeapSize * 1.5); // 150%

    console.log(`🧠 内存阈值设置:`);
    console.log(`   最大堆内存: ${maxHeapSize}MB`);
    console.log(`   堆内存警告阈值: ${this.heapWarningThreshold}MB (80%)`);
    console.log(`   堆内存危险阈值: ${this.heapCriticalThreshold}MB (90%)`);
    console.log(`   RSS警告阈值: ${this.rssWarningThreshold}MB`);
    console.log(`   RSS危险阈值: ${this.rssCriticalThreshold}MB`);
  }

  start() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('🔍 内存监控已启动');
    
    this.monitorInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, this.checkInterval);
  }

  stop() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.isMonitoring = false;
      console.log('🛑 内存监控已停止');
    }
  }

  checkMemoryUsage() {
    const memUsage = process.memoryUsage();

    // 转换为MB
    const heapUsed = Math.round(memUsage.heapUsed / 1024 / 1024);
    const heapTotal = Math.round(memUsage.heapTotal / 1024 / 1024);
    const external = Math.round(memUsage.external / 1024 / 1024);
    const rss = Math.round(memUsage.rss / 1024 / 1024);

    // 计算内存使用百分比
    const heapUsagePercent = Math.round((heapUsed / this.heapCriticalThreshold) * 100);
    const rssUsagePercent = Math.round((rss / this.rssCriticalThreshold) * 100);

    console.log(`📊 内存使用情况: 堆内存 ${heapUsed}MB/${this.heapCriticalThreshold}MB (${heapUsagePercent}%), RSS ${rss}MB, 外部 ${external}MB`);

    // 检查是否超过危险阈值（90%）
    if (heapUsed > this.heapCriticalThreshold || rss > this.rssCriticalThreshold) {
      console.error(`🚨 进程内存达到危险水平！堆内存: ${heapUsed}MB (${heapUsagePercent}%), RSS: ${rss}MB (${rssUsagePercent}%)`);
      this.handleCriticalMemory();
      return;
    }

    // 检查是否超过警告阈值（80%）
    if (heapUsed > this.heapWarningThreshold || rss > this.rssWarningThreshold) {
      this.warningCount++;
      const heapWarningPercent = Math.round((heapUsed / this.heapWarningThreshold) * 100);
      console.warn(`⚠️  内存使用达到80%阈值! 堆内存: ${heapUsed}MB (${heapWarningPercent}%), 警告次数: ${this.warningCount}/${this.maxWarnings}`);

      // 强制垃圾回收（带冷却时间）
      const now = Date.now();
      if (global.gc && (now - this.lastGcTime) > this.gcCooldown) {
        console.log('🗑️  执行垃圾回收...');
        const beforeHeap = process.memoryUsage().heapUsed;
        global.gc();
        const afterHeap = process.memoryUsage().heapUsed;
        const freed = Math.round((beforeHeap - afterHeap) / 1024 / 1024);
        console.log(`🗑️  垃圾回收完成，释放内存: ${freed}MB`);
        this.lastGcTime = now;
      }

      if (this.warningCount >= this.maxWarnings) {
        console.error('🚨 内存持续超过80%阈值，建议重启服务！');
        this.handleMemoryOverload();
      }
    } else {
      // 重置警告计数
      if (this.warningCount > 0) {
        this.warningCount = 0;
        console.log('✅ 内存使用已恢复到80%阈值以下');
      }
    }
  }

  handleMemoryOverload() {
    console.error('🚨 内存过载处理：');
    console.error('1. 清理缓存数据');
    console.error('2. 建议重启服务器');

    // 可以在这里添加自动重启逻辑
    // process.exit(1); // 退出进程，让进程管理器重启
  }

  handleCriticalMemory() {
    console.error('🚨🚨🚨 进程内存达到危险水平！');
    console.error('立即执行紧急内存清理...');

    const beforeMem = process.memoryUsage();

    // 连续执行多次垃圾回收
    if (global.gc) {
      for (let i = 0; i < 3; i++) {
        console.log(`🗑️  执行第${i + 1}次紧急垃圾回收...`);
        global.gc();
      }
    }

    // 清理可能的内存泄漏
    this.clearPotentialLeaks();

    const afterMem = process.memoryUsage();
    const heapFreed = Math.round((beforeMem.heapUsed - afterMem.heapUsed) / 1024 / 1024);
    const rssFreed = Math.round((beforeMem.rss - afterMem.rss) / 1024 / 1024);

    console.log(`🗑️  紧急清理完成: 堆内存释放${heapFreed}MB, RSS释放${rssFreed}MB`);

    const finalHeapUsed = Math.round(afterMem.heapUsed / 1024 / 1024);
    const finalUsagePercent = Math.round((finalHeapUsed / this.heapCriticalThreshold) * 100);

    if (finalHeapUsed > this.heapCriticalThreshold) {
      console.error(`⚠️  内存仍然过高 (${finalUsagePercent}%)，建议立即重启服务器以避免崩溃！`);
    } else if (finalHeapUsed > this.heapWarningThreshold) {
      console.warn(`⚠️  内存仍在警告范围 (${finalUsagePercent}%)，继续监控`);
    } else {
      console.log(`✅ 内存已降至安全水平 (${finalUsagePercent}%)`);
    }
  }

  clearPotentialLeaks() {
    try {
      // 清理可能的全局变量
      if (global.linkCache) {
        delete global.linkCache;
        console.log('🧹 清理链接缓存');
      }

      if (global.deviceCache) {
        delete global.deviceCache;
        console.log('🧹 清理设备缓存');
      }

      // 强制清理定时器
      const highestTimeoutId = setTimeout(() => {}, 0);
      for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i);
      }

      console.log('🧹 内存清理完成');
    } catch (error) {
      console.error('内存清理失败:', error.message);
    }
  }

  // 获取详细内存信息（包含阈值和状态）
  getMemoryInfo() {
    const memUsage = process.memoryUsage();
    const heapUsed = Math.round(memUsage.heapUsed / 1024 / 1024);
    const rss = Math.round(memUsage.rss / 1024 / 1024);

    return {
      heap: {
        used: heapUsed,
        total: Math.round(memUsage.heapTotal / 1024 / 1024),
        warningThreshold: this.heapWarningThreshold,
        criticalThreshold: this.heapCriticalThreshold,
        usagePercent: Math.round((heapUsed / this.heapCriticalThreshold) * 100)
      },
      rss: {
        used: rss,
        warningThreshold: this.rssWarningThreshold,
        criticalThreshold: this.rssCriticalThreshold,
        usagePercent: Math.round((rss / this.rssCriticalThreshold) * 100)
      },
      external: Math.round(memUsage.external / 1024 / 1024),
      status: this.getMemoryStatus(heapUsed, rss),
      monitoring: {
        isActive: this.isMonitoring,
        warningCount: this.warningCount,
        lastGcTime: this.lastGcTime
      }
    };
  }

  // 获取内存状态
  getMemoryStatus(heapUsed, rss) {
    if (heapUsed > this.heapCriticalThreshold || rss > this.rssCriticalThreshold) {
      return 'CRITICAL'; // 危险 (90%+)
    } else if (heapUsed > this.heapWarningThreshold || rss > this.rssWarningThreshold) {
      return 'WARNING'; // 警告 (80%+)
    } else {
      return 'NORMAL'; // 正常 (<80%)
    }
  }
}

module.exports = new MemoryMonitor();

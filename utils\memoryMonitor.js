const os = require('os');

class MemoryMonitor {
  constructor() {
    this.isMonitoring = false;
    this.memoryThreshold = 0.85; // 85%内存使用率阈值
    this.checkInterval = 30000; // 30秒检查一次
    this.warningCount = 0;
    this.maxWarnings = 3; // 连续3次警告后采取行动
  }

  start() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('🔍 内存监控已启动');
    
    this.monitorInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, this.checkInterval);
  }

  stop() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.isMonitoring = false;
      console.log('🛑 内存监控已停止');
    }
  }

  checkMemoryUsage() {
    const memUsage = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;
    const memoryUsagePercent = usedMem / totalMem;

    // 转换为MB
    const heapUsed = Math.round(memUsage.heapUsed / 1024 / 1024);
    const heapTotal = Math.round(memUsage.heapTotal / 1024 / 1024);
    const external = Math.round(memUsage.external / 1024 / 1024);
    const rss = Math.round(memUsage.rss / 1024 / 1024);

    console.log(`📊 内存使用情况: 堆内存 ${heapUsed}/${heapTotal}MB, RSS ${rss}MB, 外部 ${external}MB, 系统内存使用率 ${(memoryUsagePercent * 100).toFixed(1)}%`);

    // 检查是否超过阈值
    if (memoryUsagePercent > this.memoryThreshold || heapUsed > 3000) {
      this.warningCount++;
      console.warn(`⚠️  内存使用率过高! 警告次数: ${this.warningCount}/${this.maxWarnings}`);
      
      // 强制垃圾回收
      if (global.gc) {
        console.log('🗑️  执行垃圾回收...');
        global.gc();
      }

      if (this.warningCount >= this.maxWarnings) {
        console.error('🚨 内存使用率持续过高，建议重启服务！');
        this.handleMemoryOverload();
      }
    } else {
      // 重置警告计数
      if (this.warningCount > 0) {
        this.warningCount = 0;
        console.log('✅ 内存使用率已恢复正常');
      }
    }
  }

  handleMemoryOverload() {
    console.error('🚨 内存过载处理：');
    console.error('1. 清理缓存数据');
    console.error('2. 建议重启服务器');
    
    // 可以在这里添加自动重启逻辑
    // process.exit(1); // 退出进程，让进程管理器重启
  }

  getMemoryInfo() {
    const memUsage = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    
    return {
      heap: {
        used: Math.round(memUsage.heapUsed / 1024 / 1024),
        total: Math.round(memUsage.heapTotal / 1024 / 1024)
      },
      rss: Math.round(memUsage.rss / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024),
      system: {
        total: Math.round(totalMem / 1024 / 1024),
        free: Math.round(freeMem / 1024 / 1024),
        used: Math.round((totalMem - freeMem) / 1024 / 1024),
        usagePercent: ((totalMem - freeMem) / totalMem * 100).toFixed(1)
      }
    };
  }
}

module.exports = new MemoryMonitor();

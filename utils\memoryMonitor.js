const os = require('os');

class MemoryMonitor {
  constructor() {
    this.isMonitoring = false;
    // 使用进程内存阈值而不是系统内存阈值
    this.heapThreshold = 3000; // 堆内存3GB阈值 (MB)
    this.heapCriticalThreshold = 3500; // 堆内存3.5GB危险阈值 (MB)
    this.rssThreshold = 4000; // RSS内存4GB阈值 (MB)
    this.checkInterval = 30000; // 30秒检查一次，避免过于频繁
    this.warningCount = 0;
    this.maxWarnings = 3; // 连续3次警告后采取行动
    this.lastGcTime = 0;
    this.gcCooldown = 10000; // GC冷却时间10秒，避免过于频繁
  }

  start() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('🔍 内存监控已启动');
    
    this.monitorInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, this.checkInterval);
  }

  stop() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.isMonitoring = false;
      console.log('🛑 内存监控已停止');
    }
  }

  checkMemoryUsage() {
    const memUsage = process.memoryUsage();

    // 转换为MB
    const heapUsed = Math.round(memUsage.heapUsed / 1024 / 1024);
    const heapTotal = Math.round(memUsage.heapTotal / 1024 / 1024);
    const external = Math.round(memUsage.external / 1024 / 1024);
    const rss = Math.round(memUsage.rss / 1024 / 1024);

    console.log(`📊 内存使用情况: 堆内存 ${heapUsed}/${heapTotal}MB, RSS ${rss}MB, 外部 ${external}MB`);

    // 检查是否超过危险阈值（只检查进程内存，不检查系统内存）
    if (heapUsed > this.heapCriticalThreshold || rss > this.rssThreshold) {
      console.error(`🚨 进程内存达到危险水平！堆内存: ${heapUsed}MB, RSS: ${rss}MB`);
      this.handleCriticalMemory();
      return;
    }

    // 检查是否超过警告阈值（只检查进程内存）
    if (heapUsed > this.heapThreshold) {
      this.warningCount++;
      console.warn(`⚠️  进程内存使用过高! 堆内存: ${heapUsed}MB, 警告次数: ${this.warningCount}/${this.maxWarnings}`);

      // 强制垃圾回收（带冷却时间）
      const now = Date.now();
      if (global.gc && (now - this.lastGcTime) > this.gcCooldown) {
        console.log('🗑️  执行垃圾回收...');
        const beforeHeap = process.memoryUsage().heapUsed;
        global.gc();
        const afterHeap = process.memoryUsage().heapUsed;
        const freed = Math.round((beforeHeap - afterHeap) / 1024 / 1024);
        console.log(`🗑️  垃圾回收完成，释放内存: ${freed}MB`);
        this.lastGcTime = now;
      }

      if (this.warningCount >= this.maxWarnings) {
        console.error('🚨 进程内存持续过高，建议重启服务！');
        this.handleMemoryOverload();
      }
    } else {
      // 重置警告计数
      if (this.warningCount > 0) {
        this.warningCount = 0;
        console.log('✅ 进程内存使用已恢复正常');
      }
    }
  }

  handleMemoryOverload() {
    console.error('🚨 内存过载处理：');
    console.error('1. 清理缓存数据');
    console.error('2. 建议重启服务器');

    // 可以在这里添加自动重启逻辑
    // process.exit(1); // 退出进程，让进程管理器重启
  }

  handleCriticalMemory() {
    console.error('🚨🚨🚨 进程内存达到危险水平！');
    console.error('立即执行紧急内存清理...');

    const beforeMem = process.memoryUsage();

    // 连续执行多次垃圾回收
    if (global.gc) {
      for (let i = 0; i < 3; i++) {
        console.log(`🗑️  执行第${i + 1}次紧急垃圾回收...`);
        global.gc();
      }
    }

    // 清理可能的内存泄漏
    this.clearPotentialLeaks();

    const afterMem = process.memoryUsage();
    const heapFreed = Math.round((beforeMem.heapUsed - afterMem.heapUsed) / 1024 / 1024);
    const rssFreed = Math.round((beforeMem.rss - afterMem.rss) / 1024 / 1024);

    console.log(`🗑️  紧急清理完成: 堆内存释放${heapFreed}MB, RSS释放${rssFreed}MB`);

    if (afterMem.heapUsed > this.heapCriticalThreshold) {
      console.error('⚠️  内存仍然过高，建议立即重启服务器以避免崩溃！');
    } else {
      console.log('✅ 内存已降至安全水平');
    }
  }

  clearPotentialLeaks() {
    try {
      // 清理可能的全局变量
      if (global.linkCache) {
        delete global.linkCache;
        console.log('🧹 清理链接缓存');
      }

      if (global.deviceCache) {
        delete global.deviceCache;
        console.log('🧹 清理设备缓存');
      }

      // 强制清理定时器
      const highestTimeoutId = setTimeout(() => {}, 0);
      for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i);
      }

      console.log('🧹 内存清理完成');
    } catch (error) {
      console.error('内存清理失败:', error.message);
    }
  }

  getMemoryInfo() {
    const memUsage = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    
    return {
      heap: {
        used: Math.round(memUsage.heapUsed / 1024 / 1024),
        total: Math.round(memUsage.heapTotal / 1024 / 1024)
      },
      rss: Math.round(memUsage.rss / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024),
      system: {
        total: Math.round(totalMem / 1024 / 1024),
        free: Math.round(freeMem / 1024 / 1024),
        used: Math.round((totalMem - freeMem) / 1024 / 1024),
        usagePercent: ((totalMem - freeMem) / totalMem * 100).toFixed(1)
      }
    };
  }
}

module.exports = new MemoryMonitor();

<!-- 引入jQ<PERSON>y和Bootstrap -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"></script>

<h1 class="mb-4">任务管理</h1>

<div class="card shadow mb-4">
  <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
    <h6 class="m-0 font-weight-bold text-primary">任务列表</h6>
    <a href="/tasks/create" class="btn btn-primary">
      <i class="fas fa-plus"></i> 新建任务
    </a>
  </div>
  <div class="card-body">
    <% if (tasks.length === 0) { %>
      <div class="text-center py-5">
        <i class="fas fa-tasks fa-3x text-gray-300 mb-3"></i>
        <p class="mb-3">目前没有任务</p>
        <a href="/tasks/create" class="btn btn-primary">
          <i class="fas fa-plus"></i> 创建第一个任务
        </a>
      </div>
    <% } else { %>
      <div class="table-responsive">
        <table class="table table-bordered" width="100%" cellspacing="0">
          <thead>
            <tr>
              <th>ID</th>
              <th>任务名称</th>
              <th>描述</th>
              <th>状态</th>
              <th>优先级</th>
              <th>创建时间</th>
              <th>链接数</th>
              <th>完成率</th>
              <th>成功点赞</th>
              <th>成功收藏</th>
              <th>成功率</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <% tasks.forEach(function(task) { %>
              <tr>
                <td><span class="badge bg-dark text-white"><%= task.id %></span></td>
                <td><%= task.name %></td>
                <td><%= task.description || '无描述' %></td>
                <td>
                  <% if (task.status === 'active') { %>
                    <span class="badge bg-success text-white">进行中</span>
                  <% } else if (task.status === 'paused') { %>
                    <span class="badge bg-warning text-white">已暂停</span>
                  <% } else { %>
                    <span class="badge bg-secondary text-white">已完成</span>
                  <% } %>
                </td>
                <td>
                  <% if (task.priority === 'high') { %>
                    <span class="badge bg-danger">高</span>
                  <% } else if (task.priority === 'medium') { %>
                    <span class="badge bg-primary">中</span>
                  <% } else { %>
                    <span class="badge bg-secondary">低</span>
                  <% } %>
                </td>
                <td><%= moment(task.created_at).format('YYYY-MM-DD HH:mm:ss') %></td>
                <td><%= task.link_count || 0 %></td>
                <td>
                  <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: <%= task.completion_percentage || 0 %>%;" aria-valuenow="<%= task.completion_percentage || 0 %>" aria-valuemin="0" aria-valuemax="100">
                      <%= task.completion_percentage ? task.completion_percentage.toFixed(1) : 0 %>%
                    </div>
                  </div>
                </td>
                <td>
                  <span class="badge bg-success text-white"><%= task.successLikes || 0 %></span>
                </td>
                <td>
                  <span class="badge bg-info text-white"><%= task.successCollects || 0 %></span>
                </td>
                <td>
                  <% if (task.totalOperations > 0) { %>
                    <span class="badge bg-primary text-white"><%= task.successRate %>%</span>
                  <% } else { %>
                    <span class="badge bg-secondary text-white">-</span>
                  <% } %>
                </td>
                <td>
                  <div class="btn-group">
                    <% if (task.status === 'active') { %>
                      <form action="/tasks/pause/<%= task.id %>" method="POST" class="d-inline">
                        <input type="hidden" name="returnUrl" value="<%= encodeURIComponent(originalUrl) %>">
                        <button type="submit" class="btn btn-sm btn-warning">
                          <i class="fas fa-pause"></i> 暂停
                        </button>
                      </form>
                    <% } else if (task.status === 'paused') { %>
                      <form action="/tasks/activate/<%= task.id %>" method="POST" class="d-inline">
                        <input type="hidden" name="returnUrl" value="<%= encodeURIComponent(originalUrl) %>">
                        <button type="submit" class="btn btn-sm btn-success">
                          <i class="fas fa-play"></i> 启动
                        </button>
                      </form>
                    <% } %>
                    
                    <div class="btn-group">
                      <a href="/tasks/edit/<%= task.id %>" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-edit"></i> 编辑
                      </a>
                      <a href="/tasks/<%= task.id %>" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-eye"></i> 详情
                      </a>
                      <a href="/tasks/<%= task.id %>/import" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-file-import"></i> 导入链接
                      </a>
                      <form action="/tasks/delete/<%= task.id %>" method="POST" class="d-inline">
                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('确定要删除任务 <%= task.name %> 吗？这将删除所有相关的链接和操作记录。');">
                          <i class="fas fa-trash-alt"></i> 删除
                        </button>
                      </form>
                    </div>
                  </div>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
      
      <!-- 分页控件 -->
      <% if (typeof totalPages !== 'undefined' && totalPages > 1) { %>
        <div class="d-flex justify-content-between align-items-center mt-4">
          <div class="d-flex align-items-center">
            <form id="perPageForm" method="GET" action="" class="d-flex align-items-center">
              <span class="me-2">每页显示:</span>
              <select class="form-select form-select-sm ms-2" style="width: auto;" id="perPageSelect" name="perPage" onchange="this.form.submit()">
                <option value="20" <%= (perPage || 20) == 20 ? 'selected' : '' %>>20</option>
                <option value="30" <%= (perPage || 20) == 30 ? 'selected' : '' %>>30</option>
                <option value="50" <%= (perPage || 20) == 50 ? 'selected' : '' %>>50</option>
                <option value="80" <%= (perPage || 20) == 80 ? 'selected' : '' %>>80</option>
                <option value="100" <%= (perPage || 20) == 100 ? 'selected' : '' %>>100</option>
              </select>
              <!-- 保留当前筛选参数 -->
              <% if (filters.status) { %><input type="hidden" name="status" value="<%= filters.status %>"><% } %>
              <% if (filters.priority) { %><input type="hidden" name="priority" value="<%= filters.priority %>"><% } %>
              <% if (filters.orderBy) { %><input type="hidden" name="orderBy" value="<%= filters.orderBy %>"><% } %>
              <% if (filters.orderDir) { %><input type="hidden" name="orderDir" value="<%= filters.orderDir %>"><% } %>
            </form>
          </div>
          <nav aria-label="任务列表分页">
            <ul class="pagination justify-content-center mb-0">
              <li class="page-item <%= currentPage <= 1 ? 'disabled' : '' %>">
                <a class="page-link" href="<%= getPageUrl(1) %>" aria-label="首页">
                  <span aria-hidden="true">&laquo;&laquo;</span>
                </a>
              </li>
              <li class="page-item <%= currentPage <= 1 ? 'disabled' : '' %>">
                <a class="page-link" href="<%= getPageUrl(currentPage - 1) %>" aria-label="上一页">
                  <span aria-hidden="true">&laquo;</span>
                </a>
              </li>
              
              <% 
                let startPage = Math.max(1, currentPage - 2);
                let endPage = Math.min(totalPages, startPage + 4);
                if (endPage - startPage < 4) {
                  startPage = Math.max(1, endPage - 4);
                }
              %>
              
              <% for (let i = startPage; i <= endPage; i++) { %>
                <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                  <a class="page-link" href="<%= getPageUrl(i) %>"><%= i %></a>
                </li>
              <% } %>
              
              <li class="page-item <%= currentPage >= totalPages ? 'disabled' : '' %>">
                <a class="page-link" href="<%= getPageUrl(currentPage + 1) %>" aria-label="下一页">
                  <span aria-hidden="true">&raquo;</span>
                </a>
              </li>
              <li class="page-item <%= currentPage >= totalPages ? 'disabled' : '' %>">
                <a class="page-link" href="<%= getPageUrl(totalPages) %>" aria-label="末页">
                  <span aria-hidden="true">&raquo;&raquo;</span>
                </a>
              </li>
            </ul>
          </nav>
        </div>
      <% } else if (tasks.length > 0) { %>
        <div class="d-flex justify-content-start align-items-center mt-4">
          <form id="perPageForm" method="GET" action="" class="d-flex align-items-center">
            <span class="me-2">每页显示:</span>
            <select class="form-select form-select-sm ms-2" style="width: auto;" id="perPageSelect" name="perPage" onchange="this.form.submit()">
              <option value="20" <%= (perPage || 20) == 20 ? 'selected' : '' %>>20</option>
              <option value="30" <%= (perPage || 20) == 30 ? 'selected' : '' %>>30</option>
              <option value="50" <%= (perPage || 20) == 50 ? 'selected' : '' %>>50</option>
              <option value="80" <%= (perPage || 20) == 80 ? 'selected' : '' %>>80</option>
              <option value="100" <%= (perPage || 20) == 100 ? 'selected' : '' %>>100</option>
            </select>
            <!-- 保留当前筛选参数 -->
            <% if (filters.status) { %><input type="hidden" name="status" value="<%= filters.status %>"><% } %>
            <% if (filters.priority) { %><input type="hidden" name="priority" value="<%= filters.priority %>"><% } %>
            <% if (filters.orderBy) { %><input type="hidden" name="orderBy" value="<%= filters.orderBy %>"><% } %>
            <% if (filters.orderDir) { %><input type="hidden" name="orderDir" value="<%= filters.orderDir %>"><% } %>
          </form>
        </div>
      <% } %>
    <% } %>
  </div>
</div> 
<!-- 确保Bootstrap库可用 -->
<script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/js/bootstrap.bundle.min.js"></script>

<!-- 添加jQuery库 -->
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<!-- 添加全局删除函数 -->
<script>
// 全局函数，用于确认删除并处理视觉效果
function confirmDelete(linkId) {
  // 确认删除
  if(!confirm('确定要删除此链接吗？此操作无法撤销！')) {
    return false; // 取消提交
  }
  
  // 获取行元素
  const row = document.getElementById('link-row-' + linkId);
  
  // 添加淡出效果
  if(row) {
    row.style.transition = 'opacity 0.5s';
    row.style.opacity = '0';
    
    // 500毫秒后移除行
    setTimeout(function() {
      row.remove();
      
      // 检查是否还有数据行
      const rows = document.querySelectorAll('table.table-links tbody tr');
      if(rows.length === 0) {
        const tableContainer = document.querySelector('.table-responsive');
        tableContainer.innerHTML = '<p class="text-center">此任务暂无链接数据</p>';
      }
      
      // 显示成功消息
      const alertDiv = document.createElement('div');
      alertDiv.className = 'alert alert-success alert-dismissible fade show';
      alertDiv.role = 'alert';
      alertDiv.innerHTML = `
        链接已成功删除
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      `;
      
      // 添加到页面顶部
      const container = document.querySelector('.card-body');
      container.insertBefore(alertDiv, container.firstChild);
      
      // 3秒后自动消失
      setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
          alertDiv.remove();
        }, 150);
      }, 3000);
    }, 500);
  }
  
  return true;
}

// 使用AJAX方式删除链接
function deleteLink(linkId) {
  if(!confirm('确定要删除此链接吗？此操作无法撤销！')) {
    return false;
  }
  
  // 发送AJAX请求
  fetch('/links/delete/' + linkId, {
    method: 'POST',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(function(response) {
    return response.json();
  })
  .then(function(data) {
    if(data.success) {
      // 获取行元素
      const row = document.getElementById('link-row-' + linkId);
      
      // 添加淡出效果
      if(row) {
        row.style.transition = 'opacity 0.5s';
        row.style.opacity = '0';
        
        // 500毫秒后移除行
        setTimeout(function() {
          row.remove();
          
          // 检查是否还有数据行
          const rows = document.querySelectorAll('table.table-links tbody tr');
          if(rows.length === 0) {
            const tableContainer = document.querySelector('.table-responsive');
            tableContainer.innerHTML = '<p class="text-center">此任务暂无链接数据</p>';
          }
          
          // 更新链接数量
          const countElement = document.querySelector('.card-title');
          const currentCount = parseInt(countElement.textContent.match(/\d+/)[0]) - 1;
          countElement.textContent = '任务链接 (' + currentCount + ')';
          
          // 显示成功消息
          const alertDiv = document.createElement('div');
          alertDiv.className = 'alert alert-success alert-dismissible fade show';
          alertDiv.role = 'alert';
          alertDiv.innerHTML = `
            链接已成功删除
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          `;
          
          // 添加到页面顶部
          const container = document.querySelector('.card-body');
          container.insertBefore(alertDiv, container.firstChild);
          
          // 3秒后自动消失
          setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => {
              alertDiv.remove();
            }, 150);
          }, 3000);
        }, 500);
      }
    } else {
      alert('删除失败: ' + data.message);
    }
  })
  .catch(function(error) {
    console.error('删除链接出错:', error);
    alert('删除链接时出错，请重试');
  });
  
  return false;
}

// 监听状态变更表单提交
document.addEventListener('DOMContentLoaded', function() {
  // 获取隐藏的iframe
  const iframe = document.querySelector('iframe[name="hidden-iframe"]');
  
  // 监听iframe的加载事件
  if (iframe) {
    iframe.addEventListener('load', function() {
      try {
        // 尝试获取表单提交后的结果
        const forms = document.querySelectorAll('form[action^="/links/status/"]');
        
        // 对每个状态变更表单添加提交事件监听
        forms.forEach(form => {
          form.addEventListener('submit', function(event) {
            // 获取链接ID和目标状态
            const actionParts = this.action.split('/');
            const linkId = actionParts[actionParts.length - 1];
            const targetStatus = this.querySelector('input[name="status"]').value;
            
            // 延迟执行，等待表单提交完成
            setTimeout(function() {
              // 获取链接行
              const row = document.getElementById('link-row-' + linkId);
              if (row) {
                // 更新状态标签
                const statusCell = row.querySelector('td:nth-child(7)');
                if (statusCell) {
                  let statusText = '';
                  let statusClass = '';
                  
                  if (targetStatus === 'active') {
                    statusText = '活跃';
                    statusClass = 'status-active';
                  } else if (targetStatus === 'paused') {
                    statusText = '暂停';
                    statusClass = 'status-paused';
                  } else if (targetStatus === 'completed') {
                    statusText = '完成';
                    statusClass = 'status-completed';
                  } else if (targetStatus === 'error') {
                    statusText = '错误';
                    statusClass = 'status-error';
                  }
                  
                  statusCell.innerHTML = `<span class="badge ${statusClass}">${statusText}</span>`;
                }
                
                // 更新操作按钮
                const actionCell = row.querySelector('td:nth-child(9)');
                if (actionCell) {
                  const btnGroup = actionCell.querySelector('.btn-group');
                  
                  // 移除现有的状态按钮
                  const statusForms = btnGroup.querySelectorAll('form[action^="/links/status/"]');
                  statusForms.forEach(form => form.remove());
                  
                  // 添加新的状态按钮
                  const editBtn = btnGroup.querySelector('a.btn-outline-primary');
                  const deleteForm = btnGroup.querySelector('form[action^="/links/delete/"]');
                  
                  let newStatusForm = document.createElement('form');
                  newStatusForm.method = 'POST';
                  newStatusForm.action = `/links/status/${linkId}`;
                  newStatusForm.target = 'hidden-iframe';
                  newStatusForm.style.display = 'inline';
                  
                  let hiddenInput = document.createElement('input');
                  hiddenInput.type = 'hidden';
                  hiddenInput.name = 'status';
                  
                  let statusBtn = document.createElement('button');
                  statusBtn.type = 'submit';
                  statusBtn.className = 'btn btn-sm';
                  
                  if (targetStatus === 'active') {
                    hiddenInput.value = 'paused';
                    statusBtn.className += ' btn-outline-warning';
                    statusBtn.textContent = '暂停';
                  } else {
                    hiddenInput.value = 'active';
                    statusBtn.className += ' btn-outline-success';
                    statusBtn.textContent = '激活';
                  }
                  
                  newStatusForm.appendChild(hiddenInput);
                  newStatusForm.appendChild(statusBtn);
                  
                  btnGroup.insertBefore(newStatusForm, deleteForm);
                }
              }
              
              // 显示成功消息
              const alertDiv = document.createElement('div');
              alertDiv.className = 'alert alert-success alert-dismissible fade show';
              alertDiv.role = 'alert';
              alertDiv.innerHTML = `
                链接状态已成功更新
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
              `;
              
              // 添加到页面顶部
              const container = document.querySelector('.card-body');
              container.insertBefore(alertDiv, container.firstChild);
              
              // 3秒后自动消失
              setTimeout(() => {
                alertDiv.classList.remove('show');
                setTimeout(() => {
                  alertDiv.remove();
                }, 150);
              }, 3000);
            }, 500);
          });
        });
      } catch (error) {
        console.error('处理表单提交结果错误:', error);
      }
    });
  }
});
</script>

<!-- 添加一个隐藏的iframe用于表单提交 -->
<iframe name="hidden-iframe" style="display:none;"></iframe>

<div class="d-flex justify-content-between align-items-center mb-3">
  <div class="d-flex align-items-center">
    <a href="/tasks" class="btn btn-outline-secondary me-3">返回任务列表</a>
    <h1 class="mb-0"><%= task.name %></h1>
  </div>
  <div class="d-flex align-items-center">
    <a href="/tasks/<%= task.id %>/import" class="btn btn-success me-2">导入链接</a>
    <a href="/tasks/edit/<%= task.id %>" class="btn btn-primary">编辑任务</a>
  </div>
</div>

<!-- 任务详情 -->
<div class="card mb-3">
  <div class="card-body py-3">
    <div class="row">
      <div class="col-md-8">
        <% if (task.description) { %>
          <p class="mb-2"><%= task.description %></p>
        <% } else { %>
          <p class="text-muted mb-2">无描述</p>
        <% } %>
        
        <div class="row mt-2">
          <div class="col-md-3">
            <strong>状态:</strong> 
            <span class="badge status-<%= task.status %>">
              <% if (task.status === 'active') { %>活跃
              <% } else if (task.status === 'paused') { %>暂停
              <% } else { %>完成<% } %>
            </span>
          </div>
          <div class="col-md-3">
            <strong>优先级:</strong> 
            <span class="badge priority-<%= task.priority %>">
              <% if (task.priority === 'high') { %>高
              <% } else if (task.priority === 'medium') { %>中
              <% } else { %>低<% } %>
            </span>
          </div>
          <div class="col-md-3">
            <strong>目标点赞数:</strong> <%= task.target_likes %>
          </div>
          <div class="col-md-3">
            <strong>目标收藏数:</strong> <%= task.target_collects %>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card">
          <div class="card-body py-2">
            <h5 class="card-title mb-2">任务进度</h5>
            <div class="progress mb-2" style="height: 25px;">
              <div class="progress-bar" role="progressbar" style="width: <%= task.progress %>%;" 
                   aria-valuenow="<%= task.progress %>" aria-valuemin="0" aria-valuemax="100">
                <%= task.progress %>%
              </div>
            </div>
            <div class="row text-center">
              <div class="col">
                <h5 class="mb-0"><%= task.linkCount %></h5>
                <p class="text-muted mb-0">总链接</p>
              </div>
              <div class="col">
                <h5 class="mb-0"><%= task.activeCount %></h5>
                <p class="text-muted mb-0">活跃</p>
              </div>
              <div class="col">
                <h5 class="mb-0"><%= task.completedCount %></h5>
                <p class="text-muted mb-0">已完成</p>
              </div>
              <div class="col">
                <h5 class="mb-0"><%= task.errorCount %></h5>
                <p class="text-muted mb-0">错误</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 操作统计 -->
<div class="card mb-3">
  <div class="card-body py-3">
    <h5 class="card-title mb-3">操作统计</h5>
    <div class="row">
      <!-- 成功操作统计 -->
      <div class="col-md-6">
        <div class="card border-success">
          <div class="card-header bg-success text-white">
            <h6 class="mb-0">成功操作</h6>
          </div>
          <div class="card-body py-2">
            <div class="row text-center">
              <div class="col-4">
                <h5 class="mb-0 text-success"><%= task.successLikes || 0 %></h5>
                <p class="text-muted mb-0 small">成功点赞</p>
              </div>
              <div class="col-4">
                <h5 class="mb-0 text-success"><%= task.successCollects || 0 %></h5>
                <p class="text-muted mb-0 small">成功收藏</p>
              </div>
              <div class="col-4">
                <h5 class="mb-0 text-success"><%= task.successOperations || 0 %></h5>
                <p class="text-muted mb-0 small">总成功</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 失败操作统计 -->
      <div class="col-md-6">
        <div class="card border-danger">
          <div class="card-header bg-danger text-white">
            <h6 class="mb-0">失败操作</h6>
          </div>
          <div class="card-body py-2">
            <div class="row text-center">
              <div class="col-4">
                <h5 class="mb-0 text-danger"><%= task.failedLikes || 0 %></h5>
                <p class="text-muted mb-0 small">失败点赞</p>
              </div>
              <div class="col-4">
                <h5 class="mb-0 text-danger"><%= task.failedCollects || 0 %></h5>
                <p class="text-muted mb-0 small">失败收藏</p>
              </div>
              <div class="col-4">
                <h5 class="mb-0 text-danger"><%= task.failedOperations || 0 %></h5>
                <p class="text-muted mb-0 small">总失败</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 总体统计 -->
    <div class="row mt-3">
      <div class="col-md-3">
        <div class="text-center">
          <h4 class="mb-0 text-primary"><%= task.totalOperations || 0 %></h4>
          <p class="text-muted mb-0">总操作次数</p>
        </div>
      </div>
      <div class="col-md-3">
        <div class="text-center">
          <h4 class="mb-0 text-info"><%= task.successRate || 0 %>%</h4>
          <p class="text-muted mb-0">成功率</p>
        </div>
      </div>
      <div class="col-md-3">
        <div class="text-center">
          <h4 class="mb-0 text-warning"><%= task.actual_likes || 0 %></h4>
          <p class="text-muted mb-0">实际点赞增长</p>
        </div>
      </div>
      <div class="col-md-3">
        <div class="text-center">
          <h4 class="mb-0 text-secondary"><%= task.actual_collects || 0 %></h4>
          <p class="text-muted mb-0">实际收藏增长</p>
        </div>
      </div>
    </div>

    <!-- 设备参与统计 -->
    <div class="row mt-3">
      <div class="col-md-12">
        <div class="text-center">
          <h5 class="mb-0">
            <i class="fas fa-mobile-alt text-muted me-2"></i>
            <span class="text-primary"><%= task.participatingDevices || 0 %></span> 台设备参与此任务
          </h5>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 任务操作 -->
<div class="card mb-3">
  <div class="card-body py-3">
    <h5 class="card-title mb-2">任务操作</h5>
    <div class="row">
      <div class="col-md-3">
        <form method="POST" action="/tasks/action/<%= task.id %>">
          <input type="hidden" name="action" value="activate">
          <button type="submit" class="btn btn-success w-100">激活所有链接</button>
        </form>
      </div>
      <div class="col-md-3">
        <form method="POST" action="/tasks/action/<%= task.id %>">
          <input type="hidden" name="action" value="pause">
          <button type="submit" class="btn btn-warning w-100">暂停所有链接</button>
        </form>
      </div>
      <div class="col-md-3">
        <form method="POST" action="/tasks/action/<%= task.id %>">
          <input type="hidden" name="action" value="reset">
          <button type="submit" class="btn btn-info w-100">重置失败次数</button>
        </form>
      </div>
      <div class="col-md-3">
        <form method="POST" action="/tasks/action/<%= task.id %>" onsubmit="return confirm('确定要清空此任务的所有链接吗？此操作不可恢复！');">
          <input type="hidden" name="action" value="clear">
          <button type="submit" class="btn btn-danger w-100">清空所有链接</button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- 筛选器 -->
<div class="card mb-3">
  <div class="card-body py-3">
    <h5 class="card-title mb-2">链接筛选</h5>
    <form method="GET" action="/tasks/<%= task.id %>" class="row g-2">
      <div class="col-md-4">
        <label for="search" class="form-label">搜索链接</label>
        <input type="text" class="form-control" id="search" name="search" placeholder="输入链接关键字" value="<%= filters.search || '' %>">
      </div>
      <div class="col-md-4">
        <label for="status" class="form-label">状态</label>
        <select id="status" name="status" class="form-select">
          <option value="">全部</option>
          <option value="active" <%= filters.status === 'active' ? 'selected' : '' %>>活跃</option>
          <option value="paused" <%= filters.status === 'paused' ? 'selected' : '' %>>暂停</option>
          <option value="completed" <%= filters.status === 'completed' ? 'selected' : '' %>>完成</option>
          <option value="error" <%= filters.status === 'error' ? 'selected' : '' %>>错误</option>
        </select>
      </div>
      <div class="col-md-4">
        <label for="orderBy" class="form-label">排序</label>
        <select id="orderBy" name="orderBy" class="form-select">
          <option value="created_at" <%= filters.orderBy === 'created_at' ? 'selected' : '' %>>创建时间</option>
          <option value="last_operation_time" <%= filters.orderBy === 'last_operation_time' ? 'selected' : '' %>>最后操作时间</option>
          <option value="like_count" <%= filters.orderBy === 'like_count' ? 'selected' : '' %>>点赞次数</option>
          <option value="fail_count" <%= filters.orderBy === 'fail_count' ? 'selected' : '' %>>失败次数</option>
        </select>
      </div>
      <div class="col-12">
        <button type="submit" class="btn btn-primary">应用筛选</button>
        <a href="/tasks/<%= task.id %>" class="btn btn-secondary">重置</a>
      </div>
    </form>
  </div>
</div>

<!-- 链接列表 -->
<div class="card">
  <div class="card-body">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <h5 class="card-title mb-0">任务链接 (<%= links.length %>)</h5>
      <div>
        <a href="/tasks/<%= task.id %>/export<%= originalUrl ? originalUrl.replace('/tasks/' + task.id, '') : '' %>" class="btn btn-sm btn-success">
          <i class="bi bi-file-earmark-excel"></i> 导出为CSV
        </a>
      </div>
    </div>
    
    <% if (links && links.length > 0) { %>
      <div class="table-responsive">
        <table class="table table-striped table-links">
          <thead>
            <tr>
              <th>ID</th>
              <th>链接</th>
              <th>初始点赞</th>
              <th>当前点赞</th>
              <th>初始收藏</th>
              <th>当前收藏</th>
              <th>点赞/目标</th>
              <th>收藏/目标</th>
              <th>失败次数</th>
              <th>优先级</th>
              <th>状态</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <% links.forEach(link => { %>
              <tr id="link-row-<%= link.id %>">
                <td><span class="badge bg-dark text-white"><%= link.id %></span></td>
                <td>
                  <a href="<%= link.url %>" target="_blank" class="text-truncate d-inline-block" style="max-width: 200px;">
                    <%= link.url %>
                  </a>
                </td>
                <td><%= link.original_likes %></td>
                <td>
                  <%= link.current_likes %>
                  <% if (link.current_likes > link.original_likes) { %>
                    <span class="text-success">(+<%= link.current_likes - link.original_likes %>)</span>
                  <% } %>
                </td>
                <td><%= link.original_collects %></td>
                <td>
                  <%= link.current_collects %>
                  <% if (link.current_collects > link.original_collects) { %>
                    <span class="text-success">(+<%= link.current_collects - link.original_collects %>)</span>
                  <% } %>
                </td>
                <td>
                  <% const actualLikeIncrease = link.current_likes - link.original_likes; %>
                  <%= actualLikeIncrease %>/<%= link.target_likes %>
                  <% if (link.target_likes > 0) { %>
                    (<%= Math.round((actualLikeIncrease / link.target_likes) * 100) %>%)
                  <% } %>
                </td>
                <td>
                  <% const actualCollectIncrease = link.current_collects - link.original_collects; %>
                  <%= actualCollectIncrease %>/<%= link.target_collects %>
                  <% if (link.target_collects > 0) { %>
                    (<%= Math.round((actualCollectIncrease / link.target_collects) * 100) %>%)
                  <% } %>
                </td>
                <td><%= link.fail_count %></td>
                <td>
                  <span class="badge priority-<%= link.priority %>">
                    <% if (link.priority === 'high') { %>高
                    <% } else if (link.priority === 'medium') { %>中
                    <% } else { %>低<% } %>
                  </span>
                </td>
                <td>
                  <span class="badge status-<%= link.status %>">
                    <% if (link.status === 'active') { %>活跃
                    <% } else if (link.status === 'paused') { %>暂停
                    <% } else if (link.status === 'completed') { %>完成
                    <% } else { %>错误<% } %>
                  </span>
                </td>
                <td><%= moment(link.created_at).format('YYYY-MM-DD') %></td>
                <td>
                  <div class="btn-group">
                    <a href="/links/edit/<%= link.id %>?referer=/tasks/<%= task.id %>" class="btn btn-sm btn-outline-primary">编辑</a>
                    <% if (link.status === 'active') { %>
                      <form method="POST" action="/links/status/<%= link.id %>" style="display: inline;">
                        <input type="hidden" name="status" value="paused">
                        <input type="hidden" name="returnUrl" value="<%= encodeURIComponent(originalUrl) %>">
                        <button type="submit" class="btn btn-sm btn-outline-warning">暂停</button>
                      </form>
                    <% } else if (link.status === 'paused') { %>
                      <form method="POST" action="/links/status/<%= link.id %>" style="display: inline;">
                        <input type="hidden" name="status" value="active">
                        <input type="hidden" name="returnUrl" value="<%= encodeURIComponent(originalUrl) %>">
                        <button type="submit" class="btn btn-sm btn-outline-success">激活</button>
                      </form>
                    <% } %>
                    <form method="POST" action="/links/delete/<%= link.id %>" style="display: inline;" onsubmit="return confirm('确定要删除此链接吗？此操作无法撤销！');">
                      <input type="hidden" name="redirect_to" value="/tasks/<%= task.id %>">
                      <button type="submit" class="btn btn-sm btn-outline-danger">删除</button>
                    </form>
                  </div>
                </td>
              </tr>
            <% }) %>
          </tbody>
        </table>
      </div>
      
      <!-- 分页控件 -->
      <% if (typeof totalPages !== 'undefined' && totalPages > 1) { %>
        <div class="d-flex justify-content-between align-items-center mt-4">
          <div class="d-flex align-items-center">
            <form id="perPageForm" method="GET" action="" class="d-flex align-items-center">
              <span class="me-2">每页显示:</span>
              <select class="form-select form-select-sm ms-2" style="width: auto;" id="perPageSelect" name="perPage" onchange="this.form.submit()">
                <option value="20" <%= (perPage || 20) == 20 ? 'selected' : '' %>>20</option>
                <option value="30" <%= (perPage || 20) == 30 ? 'selected' : '' %>>30</option>
                <option value="50" <%= (perPage || 20) == 50 ? 'selected' : '' %>>50</option>
                <option value="80" <%= (perPage || 20) == 80 ? 'selected' : '' %>>80</option>
                <option value="100" <%= (perPage || 20) == 100 ? 'selected' : '' %>>100</option>
              </select>
              <!-- 保留当前筛选参数 -->
              <% if (filters.status) { %><input type="hidden" name="status" value="<%= filters.status %>"><% } %>
              <% if (filters.orderBy) { %><input type="hidden" name="orderBy" value="<%= filters.orderBy %>"><% } %>
              <% if (filters.orderDir) { %><input type="hidden" name="orderDir" value="<%= filters.orderDir %>"><% } %>
            </form>
          </div>
          <nav aria-label="任务链接分页">
            <ul class="pagination justify-content-center mb-0">
              <li class="page-item <%= currentPage <= 1 ? 'disabled' : '' %>">
                <a class="page-link" href="<%= getPageUrl(1) %>" aria-label="首页">
                  <span aria-hidden="true">&laquo;&laquo;</span>
                </a>
              </li>
              <li class="page-item <%= currentPage <= 1 ? 'disabled' : '' %>">
                <a class="page-link" href="<%= getPageUrl(currentPage - 1) %>" aria-label="上一页">
                  <span aria-hidden="true">&laquo;</span>
                </a>
              </li>
              
              <% 
                let startPage = Math.max(1, currentPage - 2);
                let endPage = Math.min(totalPages, startPage + 4);
                if (endPage - startPage < 4) {
                  startPage = Math.max(1, endPage - 4);
                }
              %>
              
              <% for (let i = startPage; i <= endPage; i++) { %>
                <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                  <a class="page-link" href="<%= getPageUrl(i) %>"><%= i %></a>
                </li>
              <% } %>
              
              <li class="page-item <%= currentPage >= totalPages ? 'disabled' : '' %>">
                <a class="page-link" href="<%= getPageUrl(currentPage + 1) %>" aria-label="下一页">
                  <span aria-hidden="true">&raquo;</span>
                </a>
              </li>
              <li class="page-item <%= currentPage >= totalPages ? 'disabled' : '' %>">
                <a class="page-link" href="<%= getPageUrl(totalPages) %>" aria-label="末页">
                  <span aria-hidden="true">&raquo;&raquo;</span>
                </a>
              </li>
            </ul>
          </nav>
        </div>
      <% } else if (links.length > 0) { %>
        <div class="d-flex justify-content-start align-items-center mt-4">
          <form id="perPageForm" method="GET" action="" class="d-flex align-items-center">
            <span class="me-2">每页显示:</span>
            <select class="form-select form-select-sm ms-2" style="width: auto;" id="perPageSelect" name="perPage" onchange="this.form.submit()">
              <option value="20" <%= (perPage || 20) == 20 ? 'selected' : '' %>>20</option>
              <option value="30" <%= (perPage || 20) == 30 ? 'selected' : '' %>>30</option>
              <option value="50" <%= (perPage || 20) == 50 ? 'selected' : '' %>>50</option>
              <option value="80" <%= (perPage || 20) == 80 ? 'selected' : '' %>>80</option>
              <option value="100" <%= (perPage || 20) == 100 ? 'selected' : '' %>>100</option>
            </select>
            <!-- 保留当前筛选参数 -->
            <% if (filters.status) { %><input type="hidden" name="status" value="<%= filters.status %>"><% } %>
            <% if (filters.orderBy) { %><input type="hidden" name="orderBy" value="<%= filters.orderBy %>"><% } %>
            <% if (filters.orderDir) { %><input type="hidden" name="orderDir" value="<%= filters.orderDir %>"><% } %>
          </form>
        </div>
      <% } %>
    <% } else { %>
      <p class="text-center">此任务暂无链接数据</p>
      <div class="text-center mt-3">
        <a href="/tasks/<%= task.id %>/import" class="btn btn-primary">导入链接</a>
      </div>
    <% } %>
  </div>
</div>

<!-- 添加CSS动画 -->
<style>
.fade-out {
  animation: fadeOut 0.5s;
  animation-fill-mode: forwards;
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* 优先级样式 */
.badge.priority-high {
  background-color: #dc3545;
  color: white;
}

.badge.priority-medium {
  background-color: #fd7e14;
  color: white;
}

.badge.priority-low {
  background-color: #6c757d;
  color: white;
}

/* 状态样式 */
.badge.status-active {
  background-color: #198754;
  color: white;
}

.badge.status-paused {
  background-color: #ffc107;
  color: black;
}

.badge.status-completed {
  background-color: #0d6efd;
  color: white;
}

.badge.status-error {
  background-color: #dc3545;
  color: white;
}
</style>

<!-- 添加JavaScript功能 -->
<script>
// 等待页面加载完成
window.onload = function() {
  console.log('页面加载完成，开始初始化删除功能');
  
  // 为所有删除按钮添加事件监听
  var deleteButtons = document.querySelectorAll('.delete-btn');
  console.log('找到删除按钮数量:', deleteButtons.length);
  
  for (var i = 0; i < deleteButtons.length; i++) {
    deleteButtons[i].onclick = function() {
      var linkId = this.getAttribute('data-id');
      console.log('点击删除按钮，链接ID:', linkId);
      
      if (confirm('确定要删除此链接吗？此操作无法撤销！')) {
        console.log('用户确认删除');
        
        // 创建一个表单并提交
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/links/delete/' + linkId;
        form.style.display = 'none';
        
        // 添加AJAX标记
        var input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'is_ajax';
        input.value = 'true';
        form.appendChild(input);
        
        // 添加表单到文档并提交
        document.body.appendChild(form);
        
        // 使用jQuery发送AJAX请求
        $.ajax({
          url: '/links/delete/' + linkId,
          type: 'POST',
          dataType: 'json',
          success: function(data) {
            console.log('删除成功，返回数据:', data);
            
            if (data.success) {
              // 获取行元素
              var row = document.getElementById('link-row-' + linkId);
              
              // 添加淡出效果
              if (row) {
                row.style.transition = 'opacity 0.5s';
                row.style.opacity = '0';
                
                // 500毫秒后移除行
                setTimeout(function() {
                  row.remove();
                  
                  // 检查是否还有数据行
                  var rows = document.querySelectorAll('table.table-links tbody tr');
                  if (rows.length === 0) {
                    var tableContainer = document.querySelector('.table-responsive');
                    tableContainer.innerHTML = '<p class="text-center">此任务暂无链接数据</p>';
                  }
                  
                  // 更新链接数量
                  var countElement = document.querySelector('.card-title');
                  var currentCount = parseInt(countElement.textContent.match(/\d+/)[0]) - 1;
                  countElement.textContent = '任务链接 (' + currentCount + ')';
                  
                  // 显示成功消息
                  var alertDiv = document.createElement('div');
                  alertDiv.className = 'alert alert-success alert-dismissible fade show';
                  alertDiv.role = 'alert';
                  alertDiv.innerHTML = '链接已成功删除 <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
                  
                  // 添加到页面顶部
                  var container = document.querySelector('.card-body');
                  container.insertBefore(alertDiv, container.firstChild);
                  
                  // 3秒后自动消失
                  setTimeout(function() {
                    alertDiv.classList.remove('show');
                    setTimeout(function() {
                      alertDiv.remove();
                    }, 150);
                  }, 3000);
                }, 500);
              }
            } else {
              alert('删除失败: ' + data.message);
            }
          },
          error: function(xhr, status, error) {
            console.error('删除链接出错:', error);
            console.error('状态码:', xhr.status);
            console.error('响应文本:', xhr.responseText);
            alert('删除链接时出错，请重试');
          }
        });
      } else {
        console.log('用户取消删除');
      }
      
      return false;
    };
  }
};
</script> 
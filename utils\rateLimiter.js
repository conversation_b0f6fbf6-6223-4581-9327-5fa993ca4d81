class RateLimiter {
  constructor() {
    this.userRequests = new Map(); // 用户请求记录
    this.cleanupInterval = 60000; // 1分钟清理一次过期记录
    
    // 定期清理过期记录
    setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);
  }

  // 检查用户是否超过速率限制
  async checkRateLimit(userId, maxRequestsPerMinute = 60) {
    const now = Date.now();
    const windowStart = now - 60000; // 1分钟窗口
    
    if (!this.userRequests.has(userId)) {
      this.userRequests.set(userId, []);
    }
    
    const userRequestTimes = this.userRequests.get(userId);
    
    // 移除超过1分钟的请求记录
    const validRequests = userRequestTimes.filter(time => time > windowStart);
    this.userRequests.set(userId, validRequests);
    
    // 检查是否超过限制
    if (validRequests.length >= maxRequestsPerMinute) {
      const oldestRequest = Math.min(...validRequests);
      const resetTime = oldestRequest + 60000;
      const waitTime = Math.ceil((resetTime - now) / 1000);
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: resetTime,
        waitTime: waitTime
      };
    }
    
    // 记录当前请求
    validRequests.push(now);
    this.userRequests.set(userId, validRequests);
    
    return {
      allowed: true,
      remaining: maxRequestsPerMinute - validRequests.length,
      resetTime: windowStart + 60000,
      waitTime: 0
    };
  }

  // 清理过期的用户记录
  cleanup() {
    const now = Date.now();
    const windowStart = now - 60000;
    
    for (const [userId, requestTimes] of this.userRequests.entries()) {
      const validRequests = requestTimes.filter(time => time > windowStart);
      
      if (validRequests.length === 0) {
        this.userRequests.delete(userId);
      } else {
        this.userRequests.set(userId, validRequests);
      }
    }
    
    console.log(`🧹 速率限制器清理完成，当前跟踪用户数: ${this.userRequests.size}`);
  }

  // 获取用户当前状态
  getUserStatus(userId) {
    const userRequestTimes = this.userRequests.get(userId) || [];
    const now = Date.now();
    const windowStart = now - 60000;
    const validRequests = userRequestTimes.filter(time => time > windowStart);
    
    return {
      requestsInLastMinute: validRequests.length,
      oldestRequestTime: validRequests.length > 0 ? Math.min(...validRequests) : null,
      newestRequestTime: validRequests.length > 0 ? Math.max(...validRequests) : null
    };
  }

  // 重置用户速率限制
  resetUser(userId) {
    this.userRequests.delete(userId);
    console.log(`🔄 重置用户(${userId})的速率限制`);
  }

  // 获取所有用户状态
  getAllUsersStatus() {
    const status = {};
    for (const [userId, requestTimes] of this.userRequests.entries()) {
      status[userId] = this.getUserStatus(userId);
    }
    return status;
  }
}

// 创建全局实例
const rateLimiter = new RateLimiter();

module.exports = rateLimiter;

const express = require('express');
const router = express.Router();
const { requireDevice } = require('../utils/auth');
const Link = require('../models/link');
const Device = require('../models/device');
const OperationLog = require('../models/operation_log');
const User = require('../models/user');
const Setting = require('../models/setting');
const DeviceConfig = require('../models/device_config');
const db = require('../utils/db');
const LinkDeviceAssignment = require('../models/link_device_assignment');
const errorMonitor = require('../utils/error_monitor');
const memoryMonitor = require('../utils/memoryMonitor');
// const distributedLock = require('../utils/distributed_lock'); // 暂时注释，等数据库表创建后启用

// API认证中间件 - 用于设备配置管理API
const requireApiAuth = async (req, res, next) => {
  try {
    // 检查是否有设备令牌或用户会话
    if (req.session && req.session.userId) {
      // 从会话获取用户
      const user = await User.getById(req.session.userId);
      if (!user || user.status !== 'active') {
        return res.status(401).json({
          success: false,
          message: '无效的用户会话或用户已禁用'
        });
      }
      req.user = user;
      return next();
    }
    
    // 从请求头获取token
    const token = req.headers['x-auth-token'];
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证token'
      });
    }
    
    // 根据token获取用户
    const user = await User.getByToken(token);
    
    if (!user || user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '无效的认证token或用户已禁用'
      });
    }
    
    // 将用户信息添加到请求对象
    req.user = user;
    next();
  } catch (error) {
    console.error('API认证错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};

// 测试连接端点 - 无需认证
router.get('/ping', (req, res) => {
  res.json({
    success: true,
    message: 'API服务正常',
    timestamp: new Date().toISOString(),
    server_version: '1.0.0'
  });
});

// 测试链接数量端点 - 无需认证，返回模拟数据
router.get('/test-link-count', (req, res) => {
  res.json({
    success: true,
    count: 150,  // 模拟数据
    message: '这是测试数据'
  });
});

// 调试路由 - 用于测试请求体解析
router.post('/debug', (req, res) => {
  console.log('收到调试请求:');
  console.log('请求头:', req.headers);
  console.log('请求体:', req.body);
  console.log('请求参数:', req.query);
  
  res.json({
    success: true,
    message: '调试请求成功',
    received_data: {
      headers: req.headers,
      body: req.body,
      query: req.query
    }
  });
});

// 设备心跳检测
router.post('/heartbeat', requireDevice, async (req, res) => {
  try {
    // 更新设备活跃时间
    await Device.updateActiveTime(req.device.id);
    
    // 从设置中获取设备每日最大操作次数
    const maxOperations = await Setting.getValue(req.user.id, 'device.max_operations_per_day', '100');
    
    // 获取设备今日操作次数
    const operationCount = await Device.getTodayOperationCount(req.device.id);
    
    res.json({
      success: true,
      message: '心跳成功',
      data: {
        operation_count: operationCount,
        max_operations: parseInt(maxOperations),
        remaining_operations: Math.max(0, parseInt(maxOperations) - operationCount)
      }
    });
  } catch (error) {
    console.error('心跳错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取下一个链接 - 添加内存优化
router.get('/next-link', requireDevice, async (req, res) => {
  // 强制垃圾回收（如果可用）
  if (global.gc && Math.random() < 0.1) { // 10%概率执行GC
    global.gc();
  }
  try {
    // 从设置中获取设备每日最大操作次数
    const maxOperations = await Setting.getValue(req.user.id, 'device.max_operations_per_day', '100');
    
    // 获取设备今日操作次数
    const operationCount = await Device.getTodayOperationCount(req.device.id);
    
    // 检查是否已达到每日操作上限
    if (operationCount >= parseInt(maxOperations)) {
      return res.json({
        success: true,
        message: '今日操作次数已达上限',
        status: 'limit_reached',
        data: {
          operation_count: operationCount,
          max_operations: parseInt(maxOperations)
        }
      });
    }
    
    // 检查是否启用性能优化
    let useOptimization = false;
    try {
      useOptimization = await Setting.getValue(req.user.id, 'link.use_optimization', 'false') === 'true';
    } catch (error) {
      console.log('获取优化设置失败，使用默认值:', error.message);
    }

    let link;
    if (useOptimization) {
      console.log(`🚀 设备(${req.device.id})尝试使用优化版链接分发`);
      try {
        // 这里暂时使用原有逻辑，避免模块依赖问题
        link = await Link.getNextAvailableLink(req.user.id, req.device.id);
        console.log(`✅ 优化版分发成功`);
      } catch (error) {
        console.log(`❌ 优化版分发失败，降级到原有逻辑:`, error.message);
        link = await Link.getNextAvailableLink(req.user.id, req.device.id);
      }
    } else {
      console.log(`🔗 设备(${req.device.id})使用标准链接分发`);
      link = await Link.getNextAvailableLink(req.user.id, req.device.id);
    }

    if (!link) {
      console.log(`🔗 设备(${req.device.id})无链接`);
      return res.json({
        success: true,
        message: '当前没有可操作的链接',
        status: 'no_links',
        data: {
          operation_count: operationCount,
          max_operations: parseInt(maxOperations)
        }
      });
    }

    console.log(`🎯 设备(${req.device.id}) → 链接(${link.id}) 点赞:${link.current_likes}/${link.target_likes}`);
    
    // 计算实际增加的点赞和收藏数
    const actualLikeIncrease = link.current_likes - link.original_likes;
    const actualCollectIncrease = link.current_collects - link.original_collects;
    
    // 检查是否所有目标都已达成
    const likesCompleted = link.target_likes === 0 || actualLikeIncrease >= link.target_likes;
    const collectsCompleted = link.target_collects === 0 || actualCollectIncrease >= link.target_collects;
    const isCompleted = likesCompleted && collectsCompleted;
    
    res.json({
      success: true,
      message: '获取链接成功',
      status: 'success',
      data: {
        link_id: link.id,
        url: link.url,
        task_id: link.task_id,
        
        // 点赞相关信息
        original_likes: link.original_likes,
        current_likes: link.current_likes,
        target_likes: link.target_likes,
        actual_like_increase: actualLikeIncrease,
        like_operations: link.like_operations || 0,
        like_count: link.like_count,
        
        // 收藏相关信息
        original_collects: link.original_collects,
        current_collects: link.current_collects,
        target_collects: link.target_collects,
        actual_collect_increase: actualCollectIncrease,
        collect_operations: link.collect_operations || 0,
        collect_count: link.collect_count,
        
        // 任务完成状态
        is_completed: isCompleted,
        
        // 设备操作信息
        operation_count: operationCount,
        max_operations: parseInt(maxOperations),
        remaining_operations: Math.max(0, parseInt(maxOperations) - operationCount)
      }
    });
  } catch (error) {
    console.error('获取链接错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误: ' + error.message
    });
  }
});

// 更新链接操作状态
router.post('/update-status', requireDevice, async (req, res) => {
  const requestId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const {
    link_id, status,
    before_like_count, before_collect_count,
    operation_type, error_message
  } = req.body;

  // 详细的请求日志
  console.log(`🔄 [${requestId}] 开始处理更新状态请求`);
  console.log(`📋 [${requestId}] 请求参数:`, {
    device_id: req.device.id,
    device_name: req.device.device_name,
    user_id: req.user.id,
    username: req.user.username,
    link_id,
    status,
    operation_type,
    before_like_count,
    before_collect_count,
    error_message
  });

  if (!link_id || !status || !operation_type) {
    console.log(`❌ [${requestId}] 参数验证失败`);
    return res.status(400).json({
      success: false,
      message: '参数不完整，需要提供link_id、status和operation_type'
    });
  }

  try {
    console.log(`📊 [${requestId}] 设备(${req.device.id}) → 链接(${link_id}) ${status} ${operation_type} 点赞:${before_like_count}`);

    // 获取链接信息
    console.log(`🔍 [${requestId}] 查询链接信息: ${link_id}`);
    const link = await Link.getById(link_id);

    if (!link) {
      console.log(`❌ [${requestId}] 链接不存在: ${link_id}`);
      return res.status(404).json({
        success: false,
        message: '链接不存在'
      });
    }

    console.log(`📄 [${requestId}] 链接信息:`, {
      id: link.id,
      user_id: link.user_id,
      status: link.status,
      current_likes: link.current_likes,
      current_collects: link.current_collects,
      like_operations: link.like_operations,
      collect_operations: link.collect_operations
    });

    // 检查链接是否属于该用户
    if (link.user_id !== req.user.id) {
      console.log(`❌ [${requestId}] 权限验证失败: 链接属于用户${link.user_id}, 当前用户${req.user.id}`);
      return res.status(403).json({
        success: false,
        message: '无权操作该链接'
      });
    }
    
    // 检查设备是否有该链接的活跃分配 - 暂时放宽此限制
    // const activeAssignment = await LinkDeviceAssignment.getDeviceActiveAssignment(req.device.id);
    // if (!activeAssignment || activeAssignment.link_id !== parseInt(link_id)) {
    //   return res.status(400).json({
    //     success: false,
    //     message: '该设备没有此链接的活跃分配，无法更新状态'
    //   });
    // }
    
    // 计算操作类型对应的计数
    let likeCount = 0;
    let collectCount = 0;
    
    if (status === 'success') {
      if (operation_type === 'like' || operation_type === 'both') {
        likeCount = 1;
      }
      
      if (operation_type === 'collect' || operation_type === 'both') {
        collectCount = 1;
      }
    }
    
    // 使用事务保护整个更新过程
    console.log(`🔄 [${requestId}] 开始事务处理更新状态`);
    await db.transaction(async (connection) => {
      // 在事务中获取设备的活跃分配
      const [assignmentResults] = await connection.query(
        'SELECT lda.*, l.url, l.task_id FROM link_device_assignments lda JOIN links l ON lda.link_id = l.id WHERE lda.device_id = ? AND lda.status = "processing" FOR UPDATE',
        [req.device.id]
      );
      const activeAssignment = assignmentResults.length > 0 ? assignmentResults[0] : null;
      console.log(`🔍 [${requestId}] 设备活跃分配:`, activeAssignment);

      let assignmentHandled = false;

      if (activeAssignment && activeAssignment.link_id === parseInt(link_id)) {
        console.log(`✏️ [${requestId}] 更新现有分配状态`);
        // 更新现有分配状态
        const [updateResult] = await connection.query(
          'UPDATE link_device_assignments SET status = ?, updated_at = NOW() WHERE link_id = ? AND device_id = ? AND status = "processing"',
          [status === 'success' ? 'completed' : 'failed', link_id, req.device.id]
        );

        if (updateResult.affectedRows > 0) {
          assignmentHandled = true;
          console.log(`✅ [${requestId}] 成功更新现有分配状态`);
        } else {
          console.warn(`⚠️ [${requestId}] 更新现有分配状态失败，可能已被其他请求处理`);
        }
      }

      if (!assignmentHandled) {
        console.log(`➕ [${requestId}] 创建新的分配记录`);
        // 创建新的分配记录并立即标记为完成
        try {
          const [insertResult] = await connection.query(
            'INSERT INTO link_device_assignments (link_id, device_id, status, assigned_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [link_id, req.device.id, status === 'success' ? 'completed' : 'failed']
          );
          console.log(`📝 [${requestId}] 新分配ID: ${insertResult.insertId}`);
        } catch (insertError) {
          // 如果插入失败（可能是重复键），尝试更新
          if (insertError.code === 'ER_DUP_ENTRY') {
            console.log(`🔄 [${requestId}] 检测到重复分配，尝试更新现有记录`);
            await connection.query(
              'UPDATE link_device_assignments SET status = ?, updated_at = NOW() WHERE link_id = ? AND device_id = ?',
              [status === 'success' ? 'completed' : 'failed', link_id, req.device.id]
            );
          } else {
            throw insertError;
          }
        }
      }
    });

    // 使用新方法更新链接操作状态和计数（已包含事务保护）
    console.log(`🔄 [${requestId}] 开始更新链接操作状态`);
    await Link.updateOperationStatus(
      link_id,
      req.device.id,
      status,
      operation_type,
      before_like_count,
      before_collect_count,
      error_message
    );
    console.log(`✅ [${requestId}] 链接操作状态更新完成`);
    
    // 创建操作日志
    console.log(`📝 [${requestId}] 创建操作日志`);
    const logData = {
      user_id: req.user.id,
      device_id: req.device.id,
      link_id: link_id,
      task_id: link.task_id,
      operation_type: operation_type,
      status: status === 'success' ? 'success' : 'fail',
      like_count: likeCount,
      collect_count: collectCount,
      error_message: error_message || ''
    };
    console.log(`📋 [${requestId}] 日志数据:`, logData);

    const logId = await OperationLog.create(logData);
    console.log(`✅ [${requestId}] 操作日志创建完成, ID: ${logId}`);
    
    // 如果操作成功，更新链接的当前点赞/收藏数和增量计数
    if (status === 'success') {
      // 更新链接的操作计数
      await db.query('UPDATE links SET like_count = like_count + ?, collect_count = collect_count + ?, last_operation_time = NOW() WHERE id = ?', [
        likeCount,
        collectCount,
        link_id
      ]);
      
      // 如果链接有关联的任务，更新任务的实际操作数量
      if (link.task_id) {
        await db.query('UPDATE tasks SET actual_likes = actual_likes + ?, actual_collects = actual_collects + ? WHERE id = ?', [
          likeCount,
          collectCount,
          link.task_id
        ]);
      }
      
      // 检查链接是否已完成目标
      await Link.checkLinkCompletion(link_id);
    } else {
      // 如果操作失败，增加失败计数
      await db.query('UPDATE links SET fail_count = fail_count + 1 WHERE id = ?', [link_id]);
    }
    
    // 获取更新后的链接信息
    const updatedLink = await Link.getById(link_id);
    
    // 计算实际增加的点赞和收藏数
    const actualLikeIncrease = updatedLink.current_likes - updatedLink.original_likes;
    const actualCollectIncrease = updatedLink.current_collects - updatedLink.original_collects;
    
    // 检查是否所有目标都已达成
    const likesCompleted = updatedLink.target_likes === 0 || actualLikeIncrease >= updatedLink.target_likes;
    const collectsCompleted = updatedLink.target_collects === 0 || actualCollectIncrease >= updatedLink.target_collects;
    const isCompleted = likesCompleted && collectsCompleted;

    console.log(`✅ 链接(${link_id}) 点赞:${updatedLink.current_likes}/${updatedLink.target_likes} ${isCompleted ? '完成' : '进行中'}`);

    res.json({
      success: true,
      message: status === 'success' ? '操作成功' : '操作失败',
      data: {
        link_id: link_id,
        original_likes: updatedLink.original_likes,
        current_likes: updatedLink.current_likes,
        target_likes: updatedLink.target_likes,
        actual_like_increase: actualLikeIncrease,
        like_operations: updatedLink.like_operations,
        like_count: updatedLink.like_count,
        
        original_collects: updatedLink.original_collects,
        current_collects: updatedLink.current_collects,
        target_collects: updatedLink.target_collects,
        actual_collect_increase: actualCollectIncrease,
        collect_operations: updatedLink.collect_operations,
        collect_count: updatedLink.collect_count,
        
        is_completed: isCompleted,
        status: updatedLink.status
      }
    });
  } catch (error) {
    console.error(`💥 [${requestId}] 更新链接状态错误:`, error);
    console.error(`💥 [${requestId}] 错误堆栈:`, error.stack);
    console.error(`💥 [${requestId}] 请求参数:`, {
      link_id,
      status,
      operation_type,
      before_like_count,
      before_collect_count,
      device_id: req.device.id,
      user_id: req.user.id
    });

    // 记录错误到监控系统
    errorMonitor.logError(requestId, '/update-status', error, {
      link_id,
      status,
      operation_type,
      device_id: req.device.id,
      user_id: req.user.id,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    // 根据错误类型返回不同的状态码和消息
    let statusCode = 500;
    let message = '服务器错误: ' + error.message;
    let retryable = false;

    if (error.code === 'ER_LOCK_WAIT_TIMEOUT') {
      statusCode = 503;
      message = '数据库繁忙，请稍后重试';
      retryable = true;
    } else if (error.code === 'ER_LOCK_DEADLOCK') {
      statusCode = 503;
      message = '数据库死锁，请稍后重试';
      retryable = true;
    } else if (error.code === 'ECONNRESET' || error.code === 'PROTOCOL_CONNECTION_LOST') {
      statusCode = 503;
      message = '数据库连接异常，请稍后重试';
      retryable = true;
    }

    res.status(statusCode).json({
      success: false,
      message: message,
      request_id: requestId,
      error_type: error.constructor.name,
      error_code: error.code,
      retryable: retryable
    });
  }
});

// 设备注册/验证
router.post('/device/register', async (req, res) => {
  // 简化日志
  // console.log('收到设备注册请求:');
  // console.log('请求体:', req.body);
  // console.log('请求头:', req.headers);
  
  const { device_token, device_name, username } = req.body;
  
  // 记录提取的参数
  // console.log('提取的参数:', { device_token, device_name, username });
  
  if (!device_token || !device_name || !username) {
    console.log('参数不完整:', { device_token, device_name, username });
    return res.status(400).json({
      success: false,
      message: '参数不完整'
    });
  }
  
  try {
    // 验证用户名
    const user = await User.getByUsername(username);
    // console.log('查询用户结果:', user ? `找到用户ID: ${user.id}` : '未找到用户');
    
    if (!user || user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户名无效或用户已停用'
      });
    }
    
    // 检查设备是否已存在
    const existingDevice = await Device.getByToken(device_token);
    
    // 获取设备配置
    const deviceConfigs = await DeviceConfig.getDefaultByUserId(user.id);
    
    if (existingDevice) {
      // 如果设备已存在，更新活跃时间
      await Device.updateActiveTime(existingDevice.id);
      
      // 从设置中获取设备每日最大操作次数
      const maxOperations = await Setting.getValue(user.id, 'device.max_operations_per_day', '100');
      
      // 获取设备今日操作次数
      const operationCount = await Device.getTodayOperationCount(existingDevice.id);
      
      return res.json({
        success: true,
        message: '设备已注册',
        data: {
          device_id: existingDevice.id,
          device_name: existingDevice.device_name,
          operation_count: operationCount,
          max_operations: parseInt(maxOperations),
          remaining_operations: Math.max(0, parseInt(maxOperations) - operationCount),
          configs: deviceConfigs
        }
      });
    }
    
    // 创建新设备
    const newDevice = await Device.create({
      user_id: user.id,
      device_token,
      device_name,
      status: 'active',
      device_type: req.body.device_type || 'mobile',
      is_temp: req.body.is_temp === 'true' || req.body.is_temp === true
    });
    
    // 从设置中获取设备每日最大操作次数
    const maxOperations = await Setting.getValue(user.id, 'device.max_operations_per_day', '100');
    
    res.json({
      success: true,
      message: '设备注册成功',
      data: {
        device_id: newDevice.insertId,
        device_name,
        operation_count: 0,
        max_operations: parseInt(maxOperations),
        remaining_operations: parseInt(maxOperations),
        configs: deviceConfigs
      }
    });
  } catch (error) {
    console.error('设备注册错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 检查并重置超时链接
// 该接口可由定时任务调用，不需要设备认证
router.post('/check-timeouts', async (req, res) => {
  try {
    const { username } = req.body;
    
    if (!username) {
      return res.status(400).json({
        success: false,
        message: '参数不完整，需要提供username'
      });
    }
    
    // 验证用户名
    const user = await User.getByUsername(username);
    
    if (!user || user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户名无效或用户已停用'
      });
    }
    
    // 获取处理超时时间设置（秒）
    const timeoutSeconds = await Setting.getValue(user.id, 'link.processing_timeout_seconds', '80');
    
    // 检查并重置超时的链接分配
    const resetCount = await LinkDeviceAssignment.checkAndResetTimeouts(user.id, timeoutSeconds);
    
    res.json({
      success: true,
      message: `检查完成，已重置 ${resetCount} 个超时链接分配`,
      data: {
        reset_count: resetCount
      }
    });
  } catch (error) {
    console.error('检查超时链接错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 设备配置API
// 获取所有配置
router.get('/device-configs', requireApiAuth, async (req, res) => {
  try {
    console.log('GET /device-configs 请求 - 用户ID:', req.user.id);
    const configs = await DeviceConfig.getAllByUserId(req.user.id);
    console.log('返回配置数量:', configs.length);
    res.json({
      success: true,
      configs
    });
  } catch (error) {
    console.error('获取配置列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取单个配置
router.get('/device-configs/:id', requireApiAuth, async (req, res) => {
  try {
    const config = await DeviceConfig.getById(req.params.id, req.user.id);
    
    if (!config) {
      return res.status(404).json({
        success: false,
        message: '配置不存在'
      });
    }
    
    res.json({
      success: true,
      config
    });
  } catch (error) {
    console.error('获取配置详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 创建配置
router.post('/device-configs', requireApiAuth, async (req, res) => {
  try {
    console.log('POST /device-configs 请求 - 用户ID:', req.user.id);
    console.log('请求数据:', req.body);
    
    const { name, description, config, is_default } = req.body;
    
    if (!name || !config) {
      return res.status(400).json({
        success: false,
        message: '配置名称和内容不能为空'
      });
    }
    
    // 验证JSON格式
    let configObj;
    try {
      configObj = typeof config === 'string' ? JSON.parse(config) : config;
    } catch (e) {
      return res.status(400).json({
        success: false,
        message: 'JSON格式错误'
      });
    }
    
    const id = await DeviceConfig.create({
      user_id: req.user.id,
      name,
      description,
      config: configObj,
      is_default: is_default || false
    });
    
    console.log('配置创建成功，ID:', id);
    res.status(201).json({
      success: true,
      message: '配置创建成功',
      id
    });
  } catch (error) {
    console.error('创建配置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 更新配置
router.put('/device-configs/:id', requireApiAuth, async (req, res) => {
  try {
    const { name, description, config, is_default } = req.body;
    
    if (!name || !config) {
      return res.status(400).json({
        success: false,
        message: '配置名称和内容不能为空'
      });
    }
    
    const existingConfig = await DeviceConfig.getById(req.params.id, req.user.id);
    
    if (!existingConfig) {
      return res.status(404).json({
        success: false,
        message: '配置不存在或无权限修改'
      });
    }
    
    // 验证JSON格式
    let configObj;
    try {
      configObj = typeof config === 'string' ? JSON.parse(config) : config;
    } catch (e) {
      return res.status(400).json({
        success: false,
        message: 'JSON格式错误'
      });
    }
    
    await DeviceConfig.update(req.params.id, req.user.id, {
      name,
      description,
      config: configObj,
      is_default: is_default || false
    });
    
    res.json({
      success: true,
      message: '配置更新成功'
    });
  } catch (error) {
    console.error('更新配置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 删除配置
router.delete('/device-configs/:id', requireApiAuth, async (req, res) => {
  try {
    const result = await DeviceConfig.delete(req.params.id, req.user.id);
    
    if (!result) {
      return res.status(404).json({
        success: false,
        message: '配置不存在或已删除'
      });
    }
    
    res.json({
      success: true,
      message: '配置已删除'
    });
  } catch (error) {
    console.error('删除配置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误: ' + error.message
    });
  }
});

// 获取当前用户的活跃链接数量
router.get('/link-count', requireApiAuth, async (req, res) => {
  try {
    const Link = require('../models/link');
    const db = require('../utils/db');

    const countSql = `
      SELECT COUNT(*) as count
      FROM links
      WHERE user_id = ? AND status = 'active'
    `;

    const [result] = await db.query(countSql, [req.user.id]);
    const linkCount = result[0].count;

    res.json({
      success: true,
      count: linkCount
    });
  } catch (error) {
    console.error('获取链接数量错误:', error);
    res.json({
      success: false,
      message: '获取链接数量失败'
    });
  }
});

// 获取优化状态统计
router.get('/optimization-stats', async (req, res) => {
  try {
    // 检查是否有优化模块
    let stats = {
      cache: {
        total: 0,
        valid: 0,
        expired: 0,
        hitRate: 0
      },
      queue: {
        totalQueues: 0,
        processingUsers: 0,
        totalPendingRequests: 0,
        maxConcurrentUsers: 10
      },
      timestamp: new Date().toISOString()
    };

    try {
      const LinkOptimizer = require('../utils/link_optimizer');
      const optimizerStats = LinkOptimizer.getStats();
      stats = { ...stats, ...optimizerStats };
    } catch (optimizerError) {
      console.log('优化器模块未启用或出错:', optimizerError.message);
    }

    res.json({
      success: true,
      stats: stats
    });
  } catch (error) {
    console.error('获取优化状态错误:', error);
    res.json({
      success: false,
      message: '获取状态失败: ' + error.message
    });
  }
});

// 内存状态API
router.get('/memory-status', async (req, res) => {
  try {
    const memoryInfo = memoryMonitor.getMemoryInfo();
    res.json({
      success: true,
      data: memoryInfo,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取内存状态失败:', error);
    res.status(500).json({
      success: false,
      error: '获取内存状态失败'
    });
  }
});

// 手动垃圾回收API
router.post('/gc', async (req, res) => {
  try {
    if (global.gc) {
      const beforeMem = process.memoryUsage();
      global.gc();
      const afterMem = process.memoryUsage();

      res.json({
        success: true,
        message: '垃圾回收执行成功',
        before: {
          heapUsed: Math.round(beforeMem.heapUsed / 1024 / 1024),
          heapTotal: Math.round(beforeMem.heapTotal / 1024 / 1024)
        },
        after: {
          heapUsed: Math.round(afterMem.heapUsed / 1024 / 1024),
          heapTotal: Math.round(afterMem.heapTotal / 1024 / 1024)
        },
        freed: Math.round((beforeMem.heapUsed - afterMem.heapUsed) / 1024 / 1024)
      });
    } else {
      res.json({
        success: false,
        message: '垃圾回收不可用，请使用 --expose-gc 参数启动'
      });
    }
  } catch (error) {
    console.error('执行垃圾回收失败:', error);
    res.status(500).json({
      success: false,
      error: '执行垃圾回收失败'
    });
  }
});

module.exports = router;
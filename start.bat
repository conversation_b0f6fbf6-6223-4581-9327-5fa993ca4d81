@echo off
echo 🚀 启动小红书服务端...

REM 创建日志目录
if not exist "logs" mkdir logs

REM 检查是否安装了PM2
pm2 --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  PM2未安装，使用普通模式启动...
    node --max-old-space-size=4096 app.js
) else (
    echo ✅ 使用PM2启动服务...
    pm2 start ecosystem.config.js --env development
    echo 📊 查看服务状态:
    pm2 status
    echo 📝 查看日志: pm2 logs xiaohongshu-server
    echo 🔄 重启服务: pm2 restart xiaohongshu-server
    echo 🛑 停止服务: pm2 stop xiaohongshu-server
)

pause

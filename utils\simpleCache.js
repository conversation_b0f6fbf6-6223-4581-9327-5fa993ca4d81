class SimpleCache {
  constructor(maxSize = 1000, ttl = 60000) { // 默认1000个条目，60秒TTL
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
    this.accessOrder = new Map(); // 用于LRU
  }

  set(key, value) {
    const now = Date.now();
    
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictOldest();
    }
    
    this.cache.set(key, {
      value: value,
      timestamp: now,
      expires: now + this.ttl
    });
    
    this.accessOrder.set(key, now);
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) {
      return null;
    }
    
    const now = Date.now();
    
    // 检查是否过期
    if (now > item.expires) {
      this.cache.delete(key);
      this.accessOrder.delete(key);
      return null;
    }
    
    // 更新访问时间
    this.accessOrder.set(key, now);
    return item.value;
  }

  has(key) {
    return this.get(key) !== null;
  }

  delete(key) {
    this.cache.delete(key);
    this.accessOrder.delete(key);
  }

  clear() {
    this.cache.clear();
    this.accessOrder.clear();
  }

  evictOldest() {
    // 找到最旧的访问记录
    let oldestKey = null;
    let oldestTime = Infinity;
    
    for (const [key, time] of this.accessOrder) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.delete(oldestKey);
    }
  }

  // 清理过期条目
  cleanup() {
    const now = Date.now();
    const keysToDelete = [];
    
    for (const [key, item] of this.cache) {
      if (now > item.expires) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.delete(key));
    return keysToDelete.length;
  }

  // 获取缓存统计信息
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      ttl: this.ttl,
      memoryUsage: this.getMemoryUsage()
    };
  }

  getMemoryUsage() {
    // 粗略估算内存使用量
    let totalSize = 0;
    for (const [key, item] of this.cache) {
      totalSize += JSON.stringify(key).length;
      totalSize += JSON.stringify(item.value).length;
      totalSize += 24; // 时间戳等元数据
    }
    return Math.round(totalSize / 1024); // 返回KB
  }
}

// 创建全局缓存实例
const settingsCache = new SimpleCache(500, 30000); // 设置缓存30秒
const linkCache = new SimpleCache(200, 10000); // 链接缓存10秒
const deviceCache = new SimpleCache(100, 60000); // 设备缓存60秒

// 定期清理过期缓存
setInterval(() => {
  const settingsCleared = settingsCache.cleanup();
  const linkCleared = linkCache.cleanup();
  const deviceCleared = deviceCache.cleanup();
  
  if (settingsCleared + linkCleared + deviceCleared > 0) {
    console.log(`🧹 缓存清理: 设置${settingsCleared}, 链接${linkCleared}, 设备${deviceCleared}`);
  }
}, 30000); // 每30秒清理一次

module.exports = {
  SimpleCache,
  settingsCache,
  linkCache,
  deviceCache
};

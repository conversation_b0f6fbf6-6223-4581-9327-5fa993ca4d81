<div class="d-flex justify-content-between align-items-center mb-4">
  <div>
    <h1>设备管理</h1>
    <% if (pagination && pagination.totalItems > 0) { %>
      <small class="text-muted">
        共 <%= pagination.totalItems %> 个设备，第 <%= pagination.currentPage %> / <%= pagination.totalPages %> 页
      </small>
    <% } %>
  </div>
  <div class="btn-group">
    <button type="button" class="btn btn-danger dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
      <i class="fas fa-trash"></i> 批量删除
    </button>
    <ul class="dropdown-menu">
      <li><a class="dropdown-item" href="javascript:void(0)" onclick="if(confirm('确定要删除3天前创建的所有设备吗？\\n\\n这将删除所有在3天前创建的设备，操作不可恢复！')){fetch('/devices/batch-delete',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({type:'3_days_ago'})}).then(r=>r.json()).then(d=>{alert('成功删除 '+d.deletedCount+' 个设备');location.reload()}).catch(e=>alert('删除失败'));}">
        <i class="fas fa-calendar-minus text-info"></i> 删除3天前的设备
      </a></li>
      <li><a class="dropdown-item" href="javascript:void(0)" onclick="if(confirm('确定要删除7天前创建的所有设备吗？\\n\\n这将删除所有在7天前创建的设备，操作不可恢复！')){fetch('/devices/batch-delete',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({type:'7_days_ago'})}).then(r=>r.json()).then(d=>{alert('成功删除 '+d.deletedCount+' 个设备');location.reload()}).catch(e=>alert('删除失败'));}">
        <i class="fas fa-calendar-week text-warning"></i> 删除7天前的设备
      </a></li>
      <li><a class="dropdown-item" href="javascript:void(0)" onclick="if(confirm('确定要删除15天前创建的所有设备吗？\\n\\n这将删除所有在15天前创建的设备，操作不可恢复！')){fetch('/devices/batch-delete',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({type:'15_days_ago'})}).then(r=>r.json()).then(d=>{alert('成功删除 '+d.deletedCount+' 个设备');location.reload()}).catch(e=>alert('删除失败'));}">
        <i class="fas fa-calendar-alt text-secondary"></i> 删除15天前的设备
      </a></li>
      <li><a class="dropdown-item" href="javascript:void(0)" onclick="if(confirm('确定要删除30天前创建的所有设备吗？\\n\\n这将删除所有在30天前创建的设备，操作不可恢复！')){fetch('/devices/batch-delete',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({type:'30_days_ago'})}).then(r=>r.json()).then(d=>{alert('成功删除 '+d.deletedCount+' 个设备');location.reload()}).catch(e=>alert('删除失败'));}">
        <i class="fas fa-history text-muted"></i> 删除30天前的设备
      </a></li>
      <li><hr class="dropdown-divider"></li>
      <li><a class="dropdown-item" href="javascript:void(0)" onclick="var d=prompt('请输入要删除的截止日期 (格式: YYYY-MM-DD)\\n将删除此日期之前创建的所有设备','2025-07-13');if(d&&d.length===10&&d.indexOf('-')>0&&confirm('确定要删除'+d+'之前创建的所有设备吗？\\n\\n操作不可恢复！')){fetch('/devices/batch-delete',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({type:'custom_date',customDate:d})}).then(r=>r.json()).then(data=>{alert('成功删除 '+data.deletedCount+' 个设备');location.reload()}).catch(e=>alert('删除失败'));}">
        <i class="fas fa-calendar-check text-primary"></i> 自定义日期删除
      </a></li>
      <li><hr class="dropdown-divider"></li>
      <li><a class="dropdown-item text-danger" href="javascript:void(0)" onclick="if(confirm('确定要删除所有从未活跃的设备吗？\\n\\n这将删除所有从未调用过API的设备，操作不可恢复！')){fetch('/devices/batch-delete',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({type:'never_active'})}).then(r=>r.json()).then(d=>{alert('成功删除 '+d.deletedCount+' 个设备');location.reload()}).catch(e=>alert('删除失败'));}">
        <i class="fas fa-ban"></i> 删除从未活跃的设备
      </a></li>
      <li><a class="dropdown-item text-danger fw-bold" href="javascript:void(0)" onclick="if(confirm('⚠️ 危险操作警告 ⚠️\\n\\n确定要删除所有设备吗？\\n\\n这将删除您账户下的所有设备！\\n此操作不可恢复，请谨慎操作！')){fetch('/devices/batch-delete',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({type:'all'})}).then(r=>r.json()).then(d=>{alert('成功删除 '+d.deletedCount+' 个设备');location.reload()}).catch(e=>alert('删除失败'));}">
        <i class="fas fa-exclamation-triangle"></i> 删除所有设备
      </a></li>
    </ul>
  </div>
</div>

<!-- 设备列表 -->
<div class="card">
  <div class="card-body">
    <% if (devices && devices.length > 0) { %>
      <div class="table-responsive">
        <table class="table table-striped table-devices">
          <thead>
            <tr>
              <th>ID</th>
              <th>设备名称</th>
              <th>设备令牌</th>
              <th>状态</th>
              <th>最后活跃时间</th>
              <th>今日操作</th>
              <th>总操作</th>
              <th>成功率</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <% devices.forEach(device => { %>
              <tr>
                <td><%= device.id %></td>
                <td><%= device.device_name %></td>
                <td>
                  <div class="input-group">
                    <input type="text" class="form-control form-control-sm" value="<%= device.device_token %>" readonly>
                    <button class="btn btn-sm btn-outline-secondary" type="button" onclick="if(navigator.clipboard){navigator.clipboard.writeText('<%= device.device_token %>').then(()=>alert('设备令牌已复制到剪贴板')).catch(()=>alert('复制失败'));}else{alert('浏览器不支持复制功能');}">
                      复制
                    </button>
                  </div>
                </td>
                <td>
                  <span class="badge <%= device.status === 'active' ? 'bg-success' : 'bg-secondary' %>">
                    <%= device.status === 'active' ? '活跃' : '停用' %>
                  </span>
                </td>
                <td>
                  <% if (device.last_active_time) { %>
                    <%= moment(device.last_active_time).format('YYYY-MM-DD HH:mm:ss') %>
                  <% } else { %>
                    从未活跃
                  <% } %>
                </td>
                <td><%= device.todayCount %></td>
                <td><%= device.totalCount %></td>
                <td><%= device.successRate.toFixed(1) %>%</td>
                <td>
                  <button type="button" class="btn btn-sm btn-outline-danger" onclick="window.location.href='/devices/delete/<%= device.id %>'">删除</button>
                </td>
              </tr>
            <% }) %>
          </tbody>
        </table>
      </div>
    <% } else { %>
      <p class="text-center">暂无设备数据</p>
    <% } %>

    <!-- 分页组件 -->
    <% if (pagination && pagination.totalPages > 1) { %>
      <nav aria-label="设备分页" class="mt-4">
        <div class="d-flex justify-content-between align-items-center">
          <!-- 每页显示数量选择 -->
          <div class="d-flex align-items-center">
            <label for="perPageSelect" class="form-label me-2 mb-0">每页显示:</label>
            <select id="perPageSelect" class="form-select form-select-sm" style="width: auto;" onchange="changePerPage(this.value)">
              <option value="10" <%= pagination.perPage == 10 ? 'selected' : '' %>>10</option>
              <option value="20" <%= pagination.perPage == 20 ? 'selected' : '' %>>20</option>
              <option value="50" <%= pagination.perPage == 50 ? 'selected' : '' %>>50</option>
              <option value="100" <%= pagination.perPage == 100 ? 'selected' : '' %>>100</option>
            </select>
          </div>

          <!-- 分页按钮 -->
          <ul class="pagination pagination-sm mb-0">
            <!-- 首页 -->
            <li class="page-item <%= !pagination.hasPrev ? 'disabled' : '' %>">
              <a class="page-link" href="?page=1&perPage=<%= pagination.perPage %>">首页</a>
            </li>

            <!-- 上一页 -->
            <li class="page-item <%= !pagination.hasPrev ? 'disabled' : '' %>">
              <a class="page-link" href="?page=<%= pagination.prevPage %>&perPage=<%= pagination.perPage %>">上一页</a>
            </li>

            <!-- 页码 -->
            <%
              let startPage = Math.max(1, pagination.currentPage - 2);
              let endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);

              if (endPage - startPage < 4) {
                if (startPage === 1) {
                  endPage = Math.min(pagination.totalPages, startPage + 4);
                } else {
                  startPage = Math.max(1, endPage - 4);
                }
              }
            %>

            <% if (startPage > 1) { %>
              <li class="page-item"><span class="page-link">...</span></li>
            <% } %>

            <% for (let i = startPage; i <= endPage; i++) { %>
              <li class="page-item <%= i === pagination.currentPage ? 'active' : '' %>">
                <a class="page-link" href="?page=<%= i %>&perPage=<%= pagination.perPage %>"><%= i %></a>
              </li>
            <% } %>

            <% if (endPage < pagination.totalPages) { %>
              <li class="page-item"><span class="page-link">...</span></li>
            <% } %>

            <!-- 下一页 -->
            <li class="page-item <%= !pagination.hasNext ? 'disabled' : '' %>">
              <a class="page-link" href="?page=<%= pagination.nextPage %>&perPage=<%= pagination.perPage %>">下一页</a>
            </li>

            <!-- 末页 -->
            <li class="page-item <%= !pagination.hasNext ? 'disabled' : '' %>">
              <a class="page-link" href="?page=<%= pagination.totalPages %>&perPage=<%= pagination.perPage %>">末页</a>
            </li>
          </ul>

          <!-- 跳转到指定页 -->
          <div class="d-flex align-items-center">
            <label for="gotoPage" class="form-label me-2 mb-0">跳转:</label>
            <input type="number" id="gotoPage" class="form-control form-control-sm me-2" style="width: 80px;" min="1" max="<%= pagination.totalPages %>" placeholder="页码">
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="gotoPage()">跳转</button>
          </div>
        </div>
      </nav>
    <% } %>
  </div>
</div>

<!-- 设备使用指南 -->
<div class="card mt-4">
  <div class="card-body">
    <h5 class="card-title">设备使用指南</h5>
    <p>添加设备后，在手机脚本中使用以下API：</p>
    
    <div class="mb-3">
      <h6>1. 设备注册</h6>
      <pre class="bg-light p-3 rounded"><code>POST /api/device/register
Content-Type: application/json

{
  "device_token": "你的设备唯一标识",
  "device_name": "设备名称",
  "user_token": "用户令牌",
  "device_type": "mobile",   // 可选
  "is_temp": false           // 可选
}</code></pre>
    </div>
    
    <div class="mb-3">
      <h6>2. 获取下一个链接</h6>
      <pre class="bg-light p-3 rounded"><code>GET /api/next-link?device_token=你的设备令牌&user_token=用户令牌</code></pre>
    </div>
    
    <div class="mb-3">
      <h6>3. 更新操作状态</h6>
      <pre class="bg-light p-3 rounded"><code>POST /api/update-status
Content-Type: application/json

{
  "device_token": "你的设备令牌",
  "user_token": "用户令牌",
  "link_id": 42,
  "status": "success",       // success, failed
  "before_like_count": 156,
  "after_like_count": 157,
  "before_collect_count": 23,  // 可选
  "after_collect_count": 24,   // 可选
  "error_message": ""        // 当status=failed时必填
}</code></pre>
    </div>
  </div>
</div>

<style>
.table-devices {
  font-size: 0.9rem;
}

.table-devices th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
}

.pagination-sm .page-link {
  padding: 0.375rem 0.75rem;
}

.form-select-sm, .form-control-sm {
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .pagination {
    justify-content: center;
  }

  .table-responsive {
    font-size: 0.8rem;
  }
}
</style>

<script>
// 改变每页显示数量
function changePerPage(perPage) {
  const urlParams = new URLSearchParams(window.location.search);
  urlParams.set('perPage', perPage);
  urlParams.set('page', '1'); // 重置到第一页
  window.location.search = urlParams.toString();
}

// 跳转到指定页
function gotoPage() {
  const pageInput = document.getElementById('gotoPage');
  const page = parseInt(pageInput.value);

  if (!page || page < 1 || page > <%= pagination ? pagination.totalPages : 1 %>) {
    alert('请输入有效的页码 (1-<%= pagination ? pagination.totalPages : 1 %>)');
    return;
  }

  const urlParams = new URLSearchParams(window.location.search);
  urlParams.set('page', page);
  window.location.search = urlParams.toString();
}

// 回车键跳转
document.addEventListener('DOMContentLoaded', function() {
  const gotoPageInput = document.getElementById('gotoPage');
  if (gotoPageInput) {
    gotoPageInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        gotoPage();
      }
    });
  }
});
</script>







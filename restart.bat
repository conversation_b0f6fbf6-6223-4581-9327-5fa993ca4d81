@echo off
echo 🔄 重启小红书服务端...

REM 杀掉所有node进程（谨慎使用）
echo 🛑 停止所有Node.js进程...
taskkill /F /IM node.exe >nul 2>&1
taskkill /F /IM nodemon.exe >nul 2>&1

REM 等待进程完全停止
timeout /t 3 >nul

REM 检查端口是否释放
echo 🔍 检查端口状态...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3001 2^>nul') do (
    echo ⚠️  端口仍被占用，强制清理...
    taskkill /F /PID %%a >nul 2>&1
)

REM 启动服务
echo 🚀 启动服务...
start "小红书服务端" cmd /k "npm run dev"

echo ✅ 重启完成！
pause
